#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 三交易所一致性修复验证程序

验证Gate.io数据阻塞问题是否已解决
测试三交易所处理逻辑的一致性
"""

import asyncio
import time
import json
import sys
import os
from datetime import datetime
from typing import Dict, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, "123"))

class ThreeExchangeConsistencyValidator:
    """三交易所一致性验证器"""
    
    def __init__(self):
        self.test_results = {
            'gate': {'processing_times': [], 'success_count': 0, 'error_count': 0},
            'bybit': {'processing_times': [], 'success_count': 0, 'error_count': 0},
            'okx': {'processing_times': [], 'success_count': 0, 'error_count': 0}
        }
        self.start_time = time.time()
        
    def log_test_result(self, message: str):
        """记录测试结果"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        print(f"[{timestamp}] {message}")
        
    async def test_gate_processing_speed(self):
        """测试Gate.io处理速度"""
        try:
            self.log_test_result("🔥 开始测试Gate.io订单簿处理速度...")
            
            # 模拟Gate.io订单簿数据
            test_data = {
                "currency_pair": "BTC_USDT",
                "asks": [["50000.1", "0.1"], ["50000.2", "0.2"], ["50000.3", "0.3"]],
                "bids": [["49999.9", "0.1"], ["49999.8", "0.2"], ["49999.7", "0.3"]],
                "u": int(time.time() * 1000)
            }
            
            # 创建Gate.io WebSocket客户端实例
            from websocket.gate_ws import GateWebSocketClient
            gate_client = GateWebSocketClient("spot")
            gate_client.symbols = ["BTC_USDT"]
            
            # 初始化必要的属性以避免错误
            gate_client.timestamp_processor = type('MockProcessor', (), {
                'get_synced_timestamp': lambda self, data: int(time.time() * 1000)
            })()
            gate_client.orderbook_locks = {}
            gate_client.last_data_time = time.time()
            
            # 测试10次处理
            processing_times = []
            for i in range(10):
                start = time.time()
                try:
                    await gate_client._handle_orderbook(test_data)
                    end = time.time()
                    processing_time = (end - start) * 1000  # 毫秒
                    processing_times.append(processing_time)
                    self.test_results['gate']['success_count'] += 1
                    self.log_test_result(f"  Gate.io处理 #{i+1}: {processing_time:.2f}ms")
                except Exception as e:
                    self.test_results['gate']['error_count'] += 1
                    self.log_test_result(f"  Gate.io处理 #{i+1} 失败: {e}")
            
            self.test_results['gate']['processing_times'] = processing_times
            avg_time = sum(processing_times) / len(processing_times) if processing_times else 0
            self.log_test_result(f"✅ Gate.io平均处理时间: {avg_time:.2f}ms")
            
        except Exception as e:
            self.log_test_result(f"❌ Gate.io测试失败: {e}")
            
    async def test_bybit_processing_speed(self):
        """测试Bybit处理速度"""
        try:
            self.log_test_result("🔥 开始测试Bybit订单簿处理速度...")
            
            # 模拟Bybit订单簿数据
            test_data = {
                "a": [["50000.1", "0.1"], ["50000.2", "0.2"], ["50000.3", "0.3"]],
                "b": [["49999.9", "0.1"], ["49999.8", "0.2"], ["49999.7", "0.3"]],
                "u": 12345,
                "seq": 123456
            }
            
            # 创建Bybit WebSocket客户端实例
            from websocket.bybit_ws import BybitWebSocketClient
            bybit_client = BybitWebSocketClient("spot")
            bybit_client.symbols = ["BTCUSDT"]
            
            # 初始化必要的属性
            bybit_client.timestamp_processor = type('MockProcessor', (), {
                'get_synced_timestamp': lambda self, data: int(time.time() * 1000)
            })()
            bybit_client.orderbook_states = {}
            bybit_client.orderbook_locks = {}
            
            # 测试10次处理
            processing_times = []
            for i in range(10):
                start = time.time()
                try:
                    await bybit_client._handle_orderbook("BTCUSDT", test_data, "snapshot")
                    end = time.time()
                    processing_time = (end - start) * 1000  # 毫秒
                    processing_times.append(processing_time)
                    self.test_results['bybit']['success_count'] += 1
                    self.log_test_result(f"  Bybit处理 #{i+1}: {processing_time:.2f}ms")
                except Exception as e:
                    self.test_results['bybit']['error_count'] += 1
                    self.log_test_result(f"  Bybit处理 #{i+1} 失败: {e}")
            
            self.test_results['bybit']['processing_times'] = processing_times
            avg_time = sum(processing_times) / len(processing_times) if processing_times else 0
            self.log_test_result(f"✅ Bybit平均处理时间: {avg_time:.2f}ms")
            
        except Exception as e:
            self.log_test_result(f"❌ Bybit测试失败: {e}")
            
    async def test_okx_processing_speed(self):
        """测试OKX处理速度"""
        try:
            self.log_test_result("🔥 开始测试OKX订单簿处理速度...")
            
            # 模拟OKX订单簿数据
            test_data = {
                "asks": [["50000.1", "0.1", "0", "1"], ["50000.2", "0.2", "0", "1"]],
                "bids": [["49999.9", "0.1", "0", "1"], ["49999.8", "0.2", "0", "1"]],
                "ts": str(int(time.time() * 1000))
            }
            
            # 创建OKX WebSocket客户端实例
            from websocket.okx_ws import OKXWebSocketClient
            okx_client = OKXWebSocketClient("spot")
            okx_client.symbols = ["BTC-USDT"]
            
            # 初始化必要的属性
            okx_client.timestamp_processor = type('MockProcessor', (), {
                'get_synced_timestamp': lambda self, data: int(time.time() * 1000)
            })()
            okx_client.orderbook_states = {}
            okx_client.orderbook_locks = {}
            okx_client.last_data_time = time.time()
            
            # 测试10次处理
            processing_times = []
            for i in range(10):
                start = time.time()
                try:
                    await okx_client._handle_orderbook("BTC-USDT", test_data)
                    end = time.time()
                    processing_time = (end - start) * 1000  # 毫秒
                    processing_times.append(processing_time)
                    self.test_results['okx']['success_count'] += 1
                    self.log_test_result(f"  OKX处理 #{i+1}: {processing_time:.2f}ms")
                except Exception as e:
                    self.test_results['okx']['error_count'] += 1
                    self.log_test_result(f"  OKX处理 #{i+1} 失败: {e}")
            
            self.test_results['okx']['processing_times'] = processing_times
            avg_time = sum(processing_times) / len(processing_times) if processing_times else 0
            self.log_test_result(f"✅ OKX平均处理时间: {avg_time:.2f}ms")
            
        except Exception as e:
            self.log_test_result(f"❌ OKX测试失败: {e}")
    
    def analyze_consistency(self):
        """分析三交易所一致性"""
        self.log_test_result("\n🔥 三交易所一致性分析")
        self.log_test_result("=" * 60)
        
        # 计算平均处理时间
        avg_times = {}
        for exchange, results in self.test_results.items():
            if results['processing_times']:
                avg_times[exchange] = sum(results['processing_times']) / len(results['processing_times'])
            else:
                avg_times[exchange] = 0
                
        # 显示结果
        self.log_test_result("\n📊 处理性能对比:")
        for exchange, avg_time in avg_times.items():
            success_rate = (self.test_results[exchange]['success_count'] / 
                          (self.test_results[exchange]['success_count'] + self.test_results[exchange]['error_count']) * 100 
                          if (self.test_results[exchange]['success_count'] + self.test_results[exchange]['error_count']) > 0 else 0)
            
            status = "✅ 正常" if avg_time < 50 and success_rate >= 90 else "⚠️ 需优化" if avg_time < 100 else "❌ 异常"
            
            self.log_test_result(f"  {exchange.upper()}: {avg_time:.2f}ms (成功率: {success_rate:.1f}%) {status}")
        
        # 检查一致性
        times_list = list(avg_times.values())
        if times_list:
            max_time = max(times_list)
            min_time = min(times_list)
            consistency_ratio = min_time / max_time if max_time > 0 else 0
            
            self.log_test_result(f"\n🎯 一致性分析:")
            self.log_test_result(f"  最快: {min_time:.2f}ms")
            self.log_test_result(f"  最慢: {max_time:.2f}ms")
            self.log_test_result(f"  一致性比率: {consistency_ratio:.2%}")
            
            if consistency_ratio >= 0.8:
                self.log_test_result("✅ 三交易所处理性能高度一致!")
            elif consistency_ratio >= 0.6:
                self.log_test_result("⚠️ 三交易所处理性能基本一致，但仍有优化空间")
            else:
                self.log_test_result("❌ 三交易所处理性能差异较大，需要进一步优化")
        
        # 验证修复效果
        gate_avg = avg_times.get('gate', 0)
        if gate_avg > 0 and gate_avg < 100:  # 小于100ms认为修复成功
            self.log_test_result(f"\n🎉 Gate.io数据阻塞问题修复成功!")
            self.log_test_result(f"   修复前: 5649ms延迟")
            self.log_test_result(f"   修复后: {gate_avg:.2f}ms处理时间")
            self.log_test_result(f"   性能提升: {5649/gate_avg:.1f}倍")
        else:
            self.log_test_result(f"\n❌ Gate.io数据阻塞问题仍需优化")
    
    async def run_validation(self):
        """运行完整验证"""
        self.log_test_result("🚀 开始三交易所一致性验证")
        self.log_test_result("=" * 60)
        
        # 并行测试三个交易所
        await asyncio.gather(
            self.test_gate_processing_speed(),
            self.test_bybit_processing_speed(), 
            self.test_okx_processing_speed()
        )
        
        # 分析结果
        self.analyze_consistency()
        
        total_time = time.time() - self.start_time
        self.log_test_result(f"\n✅ 验证完成，总耗时: {total_time:.2f}秒")

async def main():
    """主函数"""
    try:
        validator = ThreeExchangeConsistencyValidator()
        await validator.run_validation()
    except KeyboardInterrupt:
        print("\n验证被用户中断")
    except Exception as e:
        print(f"验证过程发生错误: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())