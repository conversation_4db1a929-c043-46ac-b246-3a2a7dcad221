#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 机构级质量审查系统
按照6大审查维度进行全面检查
"""

import os
import sys
import ast
import importlib.util
from typing import Dict, List, Any, Set
import re

class InstitutionalQualityAuditor:
    """机构级质量审查器"""
    
    def __init__(self):
        self.audit_results = {
            'unified_modules': {'passed': [], 'failed': [], 'score': 0},
            'no_reinventing': {'passed': [], 'failed': [], 'score': 0},
            'no_new_issues': {'passed': [], 'failed': [], 'score': 0},
            'api_compliance': {'passed': [], 'failed': [], 'score': 0},
            'functionality': {'passed': [], 'failed': [], 'score': 0},
            'consistency': {'passed': [], 'failed': [], 'score': 0}
        }
        self.base_path = "/root/myproject/123/70 gate和okx还是数据阻塞/123"
        
    def log_audit(self, message: str, level: str = "INFO"):
        """记录审查日志"""
        prefix = {
            'INFO': '📋',
            'PASS': '✅', 
            'FAIL': '❌',
            'WARN': '⚠️'
        }.get(level, '📋')
        print(f"{prefix} {message}")
        
    def audit_1_unified_modules(self):
        """审查1：是否使用了统一模块？"""
        self.log_audit("开始审查1：统一模块使用情况", "INFO")
        
        # 检查Gate.io修复是否使用统一模块
        gate_ws_path = f"{self.base_path}/websocket/gate_ws.py"
        
        try:
            with open(gate_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查统一模块导入
            unified_imports = [
                'unified_timestamp_processor',
                'unified_data_formatter', 
                'orderbook_validator',
                'performance_monitor',
                'currency_adapter'
            ]
            
            for module in unified_imports:
                if module in content:
                    self.audit_results['unified_modules']['passed'].append(f"Gate.io使用{module}")
                    self.log_audit(f"Gate.io正确使用统一模块: {module}", "PASS")
                else:
                    self.audit_results['unified_modules']['failed'].append(f"Gate.io缺少{module}")
                    self.log_audit(f"Gate.io未使用统一模块: {module}", "FAIL")
            
            # 检查是否调用统一接口
            unified_calls = [
                'get_synced_timestamp',
                'format_orderbook_data',
                'validate_orderbook_data',
                'normalize_symbol'
            ]
            
            for call in unified_calls:
                if call in content:
                    self.audit_results['unified_modules']['passed'].append(f"Gate.io调用{call}")
                    self.log_audit(f"Gate.io正确调用统一接口: {call}", "PASS")
                else:
                    self.audit_results['unified_modules']['failed'].append(f"Gate.io缺少{call}")
                    self.log_audit(f"Gate.io未调用统一接口: {call}", "FAIL")
                    
        except Exception as e:
            self.audit_results['unified_modules']['failed'].append(f"无法检查Gate.io: {e}")
            self.log_audit(f"Gate.io检查失败: {e}", "FAIL")
            
        # 计算分数
        total_checks = len(self.audit_results['unified_modules']['passed']) + len(self.audit_results['unified_modules']['failed'])
        if total_checks > 0:
            self.audit_results['unified_modules']['score'] = len(self.audit_results['unified_modules']['passed']) / total_checks * 100
            
    def audit_2_no_reinventing(self):
        """审查2：修复优化没有造车轮？"""
        self.log_audit("开始审查2：是否重复造轮子", "INFO")
        
        gate_ws_path = f"{self.base_path}/websocket/gate_ws.py"
        
        try:
            with open(gate_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否重新实现已有功能
            potential_reinventions = [
                ('def _sync_time', '应使用unified_timestamp_processor'),
                ('def _format_orderbook', '应使用unified_data_formatter'),
                ('def _validate_data', '应使用orderbook_validator'),
                ('class.*Validator', '应使用现有验证器'),
                ('class.*Formatter', '应使用现有格式化器')
            ]
            
            for pattern, suggestion in potential_reinventions:
                if re.search(pattern, content):
                    self.audit_results['no_reinventing']['failed'].append(f"可能重复实现: {pattern} - {suggestion}")
                    self.log_audit(f"发现可能的重复实现: {pattern}", "FAIL")
                else:
                    self.audit_results['no_reinventing']['passed'].append(f"未重复实现: {pattern}")
                    
            # 检查是否复用现有逻辑
            reuse_indicators = [
                'from websocket.unified',
                'from exchanges.currency_adapter',
                'get_orderbook_validator()',
                'get_orderbook_formatter()'
            ]
            
            for indicator in reuse_indicators:
                if indicator in content:
                    self.audit_results['no_reinventing']['passed'].append(f"正确复用: {indicator}")
                    self.log_audit(f"正确复用现有模块: {indicator}", "PASS")
                    
        except Exception as e:
            self.audit_results['no_reinventing']['failed'].append(f"检查失败: {e}")
            self.log_audit(f"重复造轮子检查失败: {e}", "FAIL")
            
        # 计算分数
        total_checks = len(self.audit_results['no_reinventing']['passed']) + len(self.audit_results['no_reinventing']['failed'])
        if total_checks > 0:
            self.audit_results['no_reinventing']['score'] = len(self.audit_results['no_reinventing']['passed']) / total_checks * 100
            
    def audit_3_no_new_issues(self):
        """审查3：没有引入新的问题？"""
        self.log_audit("开始审查3：是否引入新问题", "INFO")
        
        # 检查修复后的代码是否有明显问题
        gate_ws_path = f"{self.base_path}/websocket/gate_ws.py"
        
        try:
            with open(gate_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 语法检查
            try:
                ast.parse(content)
                self.audit_results['no_new_issues']['passed'].append("语法检查通过")
                self.log_audit("Gate.io代码语法检查通过", "PASS")
            except SyntaxError as e:
                self.audit_results['no_new_issues']['failed'].append(f"语法错误: {e}")
                self.log_audit(f"Gate.io代码语法错误: {e}", "FAIL")
                
            # 检查潜在问题
            potential_issues = [
                ('await.*without.*async', '异步调用问题'),
                ('undefined.*variable', '未定义变量'),
                ('infinite.*loop', '无限循环风险'),
                ('memory.*leak', '内存泄漏风险'),
                ('race.*condition', '竞态条件'),
                ('deadlock', '死锁风险')
            ]
            
            for pattern, issue_type in potential_issues:
                if re.search(pattern, content, re.IGNORECASE):
                    self.audit_results['no_new_issues']['failed'].append(f"潜在问题: {issue_type}")
                    self.log_audit(f"发现潜在问题: {issue_type}", "FAIL")
                else:
                    self.audit_results['no_new_issues']['passed'].append(f"无{issue_type}问题")
                    
            # 检查错误处理
            if 'try:' in content and 'except' in content:
                self.audit_results['no_new_issues']['passed'].append("包含错误处理")
                self.log_audit("正确包含错误处理机制", "PASS")
            else:
                self.audit_results['no_new_issues']['failed'].append("缺少错误处理")
                self.log_audit("缺少错误处理机制", "FAIL")
                
        except Exception as e:
            self.audit_results['no_new_issues']['failed'].append(f"检查失败: {e}")
            self.log_audit(f"新问题检查失败: {e}", "FAIL")
            
        # 计算分数
        total_checks = len(self.audit_results['no_new_issues']['passed']) + len(self.audit_results['no_new_issues']['failed'])
        if total_checks > 0:
            self.audit_results['no_new_issues']['score'] = len(self.audit_results['no_new_issues']['passed']) / total_checks * 100
            
    def audit_4_api_compliance(self):
        """审查4：符合三交易所API文档规则？"""
        self.log_audit("开始审查4：API规则合规性", "INFO")
        
        # 检查各交易所WebSocket实现
        exchanges = ['gate_ws.py', 'bybit_ws.py', 'okx_ws.py']
        
        for exchange_file in exchanges:
            exchange_path = f"{self.base_path}/websocket/{exchange_file}"
            exchange_name = exchange_file.replace('_ws.py', '').upper()
            
            try:
                with open(exchange_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Gate.io API规则检查
                if 'gate' in exchange_file:
                    gate_rules = [
                        ('spot.order_book', 'Gate.io现货订单簿频道'),
                        ('futures.order_book', 'Gate.io期货订单簿频道'),
                        ('100ms', 'Gate.io更新间隔'),
                        ('ping.*pong', 'Gate.io心跳机制')
                    ]
                    
                    for pattern, rule_desc in gate_rules:
                        if re.search(pattern, content):
                            self.audit_results['api_compliance']['passed'].append(f"{exchange_name}: {rule_desc}")
                            self.log_audit(f"{exchange_name}符合API规则: {rule_desc}", "PASS")
                        else:
                            self.audit_results['api_compliance']['failed'].append(f"{exchange_name}: 缺少{rule_desc}")
                            self.log_audit(f"{exchange_name}不符合API规则: {rule_desc}", "FAIL")
                            
                # Bybit API规则检查  
                elif 'bybit' in exchange_file:
                    bybit_rules = [
                        ('orderbook\\.50', 'Bybit 50档深度'),
                        ('op.*subscribe', 'Bybit订阅格式'),
                        ('ping.*pong', 'Bybit心跳机制')
                    ]
                    
                    for pattern, rule_desc in bybit_rules:
                        if re.search(pattern, content):
                            self.audit_results['api_compliance']['passed'].append(f"{exchange_name}: {rule_desc}")
                            self.log_audit(f"{exchange_name}符合API规则: {rule_desc}", "PASS")
                            
                # OKX API规则检查
                elif 'okx' in exchange_file:
                    okx_rules = [
                        ('books', 'OKX订单簿频道'),
                        ('instId', 'OKX交易对标识'),
                        ('op.*subscribe', 'OKX订阅格式')
                    ]
                    
                    for pattern, rule_desc in okx_rules:
                        if re.search(pattern, content):
                            self.audit_results['api_compliance']['passed'].append(f"{exchange_name}: {rule_desc}")
                            self.log_audit(f"{exchange_name}符合API规则: {rule_desc}", "PASS")
                            
            except Exception as e:
                self.audit_results['api_compliance']['failed'].append(f"{exchange_name}检查失败: {e}")
                self.log_audit(f"{exchange_name}API合规检查失败: {e}", "FAIL")
                
        # 计算分数
        total_checks = len(self.audit_results['api_compliance']['passed']) + len(self.audit_results['api_compliance']['failed'])
        if total_checks > 0:
            self.audit_results['api_compliance']['score'] = len(self.audit_results['api_compliance']['passed']) / total_checks * 100
            
    def audit_5_functionality(self):
        """审查5：确保功能实现？"""
        self.log_audit("开始审查5：功能完整性", "INFO")
        
        gate_ws_path = f"{self.base_path}/websocket/gate_ws.py"
        
        try:
            with open(gate_ws_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查核心功能实现
            core_functions = [
                ('_handle_orderbook', '订单簿处理'),
                ('subscribe_channels', '频道订阅'),
                ('handle_message', '消息处理'),
                ('send_heartbeat', '心跳发送'),
                ('get_ws_url', 'WebSocket URL获取')
            ]
            
            for func_name, func_desc in core_functions:
                if f"def {func_name}" in content or f"async def {func_name}" in content:
                    self.audit_results['functionality']['passed'].append(f"Gate.io实现{func_desc}")
                    self.log_audit(f"Gate.io正确实现: {func_desc}", "PASS")
                else:
                    self.audit_results['functionality']['failed'].append(f"Gate.io缺少{func_desc}")
                    self.log_audit(f"Gate.io缺少实现: {func_desc}", "FAIL")
                    
            # 检查数据处理完整性
            data_processing = [
                ('asks.*bids', '买卖盘数据处理'),
                ('timestamp', '时间戳处理'),
                ('symbol', '交易对处理'),
                ('emit.*market_data', '数据发送')
            ]
            
            for pattern, desc in data_processing:
                if re.search(pattern, content):
                    self.audit_results['functionality']['passed'].append(f"Gate.io包含{desc}")
                    self.log_audit(f"Gate.io正确包含: {desc}", "PASS")
                else:
                    self.audit_results['functionality']['failed'].append(f"Gate.io缺少{desc}")
                    self.log_audit(f"Gate.io缺少: {desc}", "FAIL")
                    
        except Exception as e:
            self.audit_results['functionality']['failed'].append(f"功能检查失败: {e}")
            self.log_audit(f"功能完整性检查失败: {e}", "FAIL")
            
        # 计算分数
        total_checks = len(self.audit_results['functionality']['passed']) + len(self.audit_results['functionality']['failed'])
        if total_checks > 0:
            self.audit_results['functionality']['score'] = len(self.audit_results['functionality']['passed']) / total_checks * 100
            
    def audit_6_consistency(self):
        """审查6：没有重复、冗余、接口不统一问题？"""
        self.log_audit("开始审查6：一致性和冗余检查", "INFO")
        
        # 检查三个交易所的一致性
        exchanges = {
            'gate': f"{self.base_path}/websocket/gate_ws.py",
            'bybit': f"{self.base_path}/websocket/bybit_ws.py", 
            'okx': f"{self.base_path}/websocket/okx_ws.py"
        }
        
        interface_patterns = {}
        
        for exchange, path in exchanges.items():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取接口模式
                interface_patterns[exchange] = {
                    'emit_pattern': bool(re.search(r'emit\(["\']market_data["\']', content)),
                    'timestamp_pattern': bool(re.search(r'get_synced_timestamp', content)),
                    'validation_pattern': bool(re.search(r'validate_orderbook_data', content)),
                    'formatter_pattern': bool(re.search(r'format_orderbook_data', content))
                }
                
            except Exception as e:
                self.audit_results['consistency']['failed'].append(f"{exchange}接口检查失败: {e}")
                self.log_audit(f"{exchange}接口检查失败: {e}", "FAIL")
                
        # 检查接口一致性
        if len(interface_patterns) >= 2:
            # 比较接口模式一致性
            reference = list(interface_patterns.values())[0]
            
            for exchange, patterns in interface_patterns.items():
                for pattern_name, pattern_value in patterns.items():
                    if pattern_value == reference.get(pattern_name):
                        self.audit_results['consistency']['passed'].append(f"{exchange}接口{pattern_name}一致")
                        self.log_audit(f"{exchange}接口{pattern_name}保持一致", "PASS")
                    else:
                        self.audit_results['consistency']['failed'].append(f"{exchange}接口{pattern_name}不一致")
                        self.log_audit(f"{exchange}接口{pattern_name}不一致", "FAIL")
        
        # 检查重复代码
        try:
            with open(f"{self.base_path}/websocket/gate_ws.py", 'r', encoding='utf-8') as f:
                gate_content = f.read()
                
            # 检查是否有重复的时间戳处理逻辑
            if gate_content.count('time.time()') > 10:
                self.audit_results['consistency']['failed'].append("Gate.io存在重复时间处理")
                self.log_audit("Gate.io存在重复时间处理逻辑", "FAIL")
            else:
                self.audit_results['consistency']['passed'].append("Gate.io时间处理无重复")
                self.log_audit("Gate.io时间处理逻辑简洁", "PASS")
                
            # 检查是否有重复的数据格式化
            if gate_content.count('float(') > 20:
                self.audit_results['consistency']['failed'].append("Gate.io存在重复数据转换")
                self.log_audit("Gate.io存在重复数据转换逻辑", "FAIL")
            else:
                self.audit_results['consistency']['passed'].append("Gate.io数据转换无重复")
                self.log_audit("Gate.io数据转换逻辑简洁", "PASS")
                
        except Exception as e:
            self.audit_results['consistency']['failed'].append(f"重复检查失败: {e}")
            self.log_audit(f"重复代码检查失败: {e}", "FAIL")
            
        # 计算分数
        total_checks = len(self.audit_results['consistency']['passed']) + len(self.audit_results['consistency']['failed'])
        if total_checks > 0:
            self.audit_results['consistency']['score'] = len(self.audit_results['consistency']['passed']) / total_checks * 100
            
    def generate_audit_report(self):
        """生成审查报告"""
        self.log_audit("\n" + "="*80, "INFO")
        self.log_audit("🔥 机构级质量审查报告", "INFO")
        self.log_audit("="*80, "INFO")
        
        total_score = 0
        
        for audit_name, results in self.audit_results.items():
            score = results['score']
            total_score += score
            
            status = "✅ PASS" if score >= 80 else "⚠️ WARN" if score >= 60 else "❌ FAIL"
            self.log_audit(f"\n📊 {audit_name.upper()}: {score:.1f}% {status}")
            
            if results['passed']:
                self.log_audit("   通过项目:")
                for passed in results['passed'][:5]:  # 显示前5项
                    self.log_audit(f"     ✅ {passed}")
                    
            if results['failed']:
                self.log_audit("   失败项目:")
                for failed in results['failed'][:5]:  # 显示前5项
                    self.log_audit(f"     ❌ {failed}")
                    
        avg_score = total_score / len(self.audit_results)
        overall_status = "✅ EXCELLENT" if avg_score >= 90 else "⚠️ GOOD" if avg_score >= 80 else "❌ NEEDS IMPROVEMENT"
        
        self.log_audit(f"\n🎯 总体质量评分: {avg_score:.1f}% {overall_status}")
        
        if avg_score >= 80:
            self.log_audit("🎉 审查通过！代码质量达到机构级标准", "PASS")
        else:
            self.log_audit("⚠️ 审查发现问题，需要改进后才能进行测试", "FAIL")
            
        return avg_score >= 80
        
    def run_full_audit(self):
        """运行完整审查"""
        self.log_audit("🚀 开始机构级质量审查", "INFO")
        
        self.audit_1_unified_modules()
        self.audit_2_no_reinventing()
        self.audit_3_no_new_issues()
        self.audit_4_api_compliance()
        self.audit_5_functionality()
        self.audit_6_consistency()
        
        return self.generate_audit_report()

if __name__ == "__main__":
    auditor = InstitutionalQualityAuditor()
    audit_passed = auditor.run_full_audit()
    
    if audit_passed:
        print("\n🎉 质量审查通过，可以进行测试阶段！")
    else:
        print("\n⚠️ 质量审查发现问题，请先修复后再测试！")
        sys.exit(1)