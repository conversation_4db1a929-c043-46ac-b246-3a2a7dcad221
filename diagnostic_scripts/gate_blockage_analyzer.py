#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 Gate.io数据积压阻塞专项分析脚本

基于日志发现的关键模式：
- 时间戳递增：1754326028886 → 1754326029171 (285ms范围内的数据)  
- 延迟递减：5922ms → 5649ms
- 这说明Gate.io在处理一批积压的旧数据！

目标：分析Gate.io数据积压的根本原因
"""

import time
import json
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

sys.path.append("/root/myproject/123/70 gate和okx还是数据阻塞/123")

class GateBlockageAnalyzer:
    """Gate.io数据积压阻塞分析器"""
    
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.gate_events = []
        self.blockage_patterns = []
        
    def analyze_gate_blockage(self):
        """分析Gate.io数据积压阻塞模式"""
        print("🚨 Gate.io数据积压阻塞专项分析")
        print("=" * 60)
        
        # 1. 解析日志中的Gate事件
        self.parse_gate_events()
        
        # 2. 识别积压模式
        self.identify_blockage_patterns()
        
        # 3. 分析根本原因
        self.analyze_root_causes()
        
        # 4. 生成修复建议
        self.generate_fix_recommendations()
        
    def parse_gate_events(self):
        """解析日志中的Gate事件"""
        print("\n📋 1. 解析Gate.io事件日志")
        print("-" * 40)
        
        try:
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if "'exchange': 'gate'" in line and "timestamp_age_ms" in line:
                        # 提取关键信息
                        try:
                            # 提取时间戳
                            time_part = line.split(' - ')[0]
                            log_time = time_part.split(',')[0]  # 去掉毫秒
                            
                            # 提取数据信息
                            if '{' in line and '}' in line:
                                data_start = line.find('{')
                                data_end = line.rfind('}') + 1
                                data_str = line[data_start:data_end]
                                
                                # 转换单引号为双引号以便JSON解析
                                data_str = data_str.replace("'", '"')
                                data_dict = json.loads(data_str)
                                
                                event = {
                                    'line_number': line_num,
                                    'log_time': log_time,
                                    'timestamp_age_ms': data_dict.get('timestamp_age_ms', 0),
                                    'discarded_timestamp': data_dict.get('discarded_timestamp', 0),
                                    'extraction_source': data_dict.get('extraction_source', ''),
                                    'raw_line': line.strip()
                                }
                                
                                self.gate_events.append(event)
                                
                        except Exception as e:
                            print(f"⚠️ 解析行{line_num}失败: {e}")
                            continue
                            
            print(f"✅ 解析完成，找到 {len(self.gate_events)} 个Gate.io延迟事件")
            
            if self.gate_events:
                first_event = self.gate_events[0]
                last_event = self.gate_events[-1]
                print(f"时间范围: {first_event['log_time']} ~ {last_event['log_time']}")
                print(f"延迟范围: {min(e['timestamp_age_ms'] for e in self.gate_events)}ms ~ {max(e['timestamp_age_ms'] for e in self.gate_events)}ms")
                
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
            
    def identify_blockage_patterns(self):
        """识别数据积压模式"""
        print("\n🔍 2. 识别数据积压模式")
        print("-" * 40)
        
        if len(self.gate_events) < 2:
            print("⚠️ 数据不足，无法分析模式")
            return
            
        # 按时间戳分组连续事件
        current_batch = []
        batches = []
        
        for i, event in enumerate(self.gate_events):
            if i == 0:
                current_batch = [event]
                continue
                
            prev_event = self.gate_events[i-1]
            
            # 如果时间戳连续且在同一秒内，认为是同一批积压数据
            time_diff = event['discarded_timestamp'] - prev_event['discarded_timestamp']
            log_time_diff = self.parse_log_time_diff(event['log_time'], prev_event['log_time'])
            
            if time_diff > 0 and time_diff < 1000 and log_time_diff < 1:  # 1秒内的连续数据
                current_batch.append(event)
            else:
                if len(current_batch) > 5:  # 超过5个连续事件认为是积压
                    batches.append(current_batch)
                current_batch = [event]
                
        # 添加最后一批
        if len(current_batch) > 5:
            batches.append(current_batch)
            
        print(f"发现 {len(batches)} 个数据积压批次")
        
        # 分析每个批次
        for i, batch in enumerate(batches):
            print(f"\n📦 积压批次 {i+1}:")
            print(f"   事件数量: {len(batch)}")
            print(f"   时间范围: {batch[0]['log_time']} ~ {batch[-1]['log_time']}")
            
            # 分析时间戳模式
            timestamps = [e['discarded_timestamp'] for e in batch]
            delays = [e['timestamp_age_ms'] for e in batch]
            
            timestamp_span = max(timestamps) - min(timestamps)
            delay_trend = "递减" if delays[0] > delays[-1] else "递增" if delays[0] < delays[-1] else "平稳"
            
            print(f"   时间戳跨度: {timestamp_span}ms")
            print(f"   延迟范围: {min(delays)}ms ~ {max(delays)}ms")
            print(f"   延迟趋势: {delay_trend}")
            
            # 计算数据积压的严重程度
            avg_delay = sum(delays) / len(delays)
            severity = "严重" if avg_delay > 5000 else "中等" if avg_delay > 2000 else "轻微"
            
            print(f"   平均延迟: {avg_delay:.1f}ms")
            print(f"   严重程度: {severity}")
            
            pattern = {
                'batch_id': i+1,
                'event_count': len(batch),
                'timestamp_span_ms': timestamp_span,
                'delay_range': [min(delays), max(delays)],
                'delay_trend': delay_trend,
                'average_delay_ms': avg_delay,
                'severity': severity,
                'start_time': batch[0]['log_time'],
                'end_time': batch[-1]['log_time'],
                'events': batch
            }
            
            self.blockage_patterns.append(pattern)
            
    def parse_log_time_diff(self, time1: str, time2: str) -> float:
        """解析日志时间差（秒）"""
        try:
            # 格式: 2025-08-04 18:47:14
            from datetime import datetime
            dt1 = datetime.strptime(time1, "%Y-%m-%d %H:%M:%S")
            dt2 = datetime.strptime(time2, "%Y-%m-%d %H:%M:%S")
            return abs((dt1 - dt2).total_seconds())
        except:
            return 0
            
    def analyze_root_causes(self):
        """分析根本原因"""
        print("\n🔬 3. 根本原因分析")
        print("-" * 40)
        
        if not self.blockage_patterns:
            print("⚠️ 未发现明显的积压模式")
            return
            
        # 分析积压特征
        total_events = sum(p['event_count'] for p in self.blockage_patterns)
        max_delay = max(max(p['delay_range']) for p in self.blockage_patterns)
        avg_batch_size = sum(p['event_count'] for p in self.blockage_patterns) / len(self.blockage_patterns)
        
        print(f"📊 积压统计:")
        print(f"   总积压事件: {total_events}")
        print(f"   最大延迟: {max_delay}ms")
        print(f"   平均批次大小: {avg_batch_size:.1f}")
        
        # 分析可能的原因
        print(f"\n🚨 可能的根本原因:")
        
        # 1. WebSocket接收缓冲区积压
        if max_delay > 5000:
            print(f"1. 🔥 WebSocket接收缓冲区积压")
            print(f"   - 特征: 大量旧数据突然涌入处理")
            print(f"   - 延迟>{max_delay}ms表明数据在缓冲区积压了很长时间")
            print(f"   - 可能原因: 网络波动或处理速度跟不上接收速度")
            
        # 2. Gate.io服务器端问题
        declining_batches = len([p for p in self.blockage_patterns if p['delay_trend'] == '递减'])
        if declining_batches > 0:
            print(f"2. 🔥 Gate.io服务器端批量数据发送")
            print(f"   - 特征: {declining_batches}个批次显示延迟递减模式")
            print(f"   - 说明服务器在处理一批积压的历史数据")
            print(f"   - 可能原因: Gate.io服务器重启后批量推送积压数据")
            
        # 3. 客户端处理阻塞
        large_batches = len([p for p in self.blockage_patterns if p['event_count'] > 20])
        if large_batches > 0:
            print(f"3. 🔥 客户端消息处理阻塞")
            print(f"   - 特征: {large_batches}个大批次(>20个事件)")
            print(f"   - 说明客户端在短时间内处理大量积压消息")
            print(f"   - 可能原因: 异步处理能力不足或消息队列阻塞")
            
    def generate_fix_recommendations(self):
        """生成修复建议"""
        print("\n💡 4. 修复建议")
        print("-" * 40)
        
        print("基于数据积压模式分析，建议以下修复方案:")
        
        print("\n🔧 立即修复:")
        print("1. 增强WebSocket重连机制")
        print("   - 检测到大量积压数据时主动重连")
        print("   - 设置更激进的重连策略")
        
        print("2. 优化消息处理流水线")
        print("   - 使用异步队列处理积压消息")
        print("   - 增加消息处理并发度")
        
        print("3. 添加积压检测机制")
        print("   - 监控连续高延迟事件")
        print("   - 自动触发清理和重连")
        
        print("\n🛠️ 中期优化:")
        print("1. 实施智能丢弃策略")
        print("   - 对于严重积压的数据，智能丢弃过旧的消息")
        print("   - 保留最新的关键数据")
        
        print("2. 增强监控和告警")
        print("   - 实时监控Gate.io数据流质量")
        print("   - 积压超过阈值时立即告警")
        
        # 保存分析报告
        try:
            report_file = f"/root/myproject/123/70 gate和okx还是数据阻塞/diagnostic_results/gate_blockage_analysis_{int(time.time())}.json"
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            report_data = {
                'analysis_time': datetime.now().isoformat(),
                'total_events': len(self.gate_events),
                'blockage_patterns': self.blockage_patterns,
                'summary': {
                    'pattern_count': len(self.blockage_patterns),
                    'max_delay_ms': max(e['timestamp_age_ms'] for e in self.gate_events) if self.gate_events else 0,
                    'total_affected_events': sum(p['event_count'] for p in self.blockage_patterns)
                }
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
                
            print(f"\n📄 详细分析报告已保存: {report_file}")
            
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")

def main():
    """主函数"""
    log_file = "/root/myproject/123/70 gate和okx还是数据阻塞/123/logs/websocket_performance_20250804.log"
    
    analyzer = GateBlockageAnalyzer(log_file)
    analyzer.analyze_gate_blockage()

if __name__ == "__main__":
    main()