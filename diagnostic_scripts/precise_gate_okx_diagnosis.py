#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 Gate.io和OKX数据阻塞精确诊断脚本
专门诊断Gate.io和OKX WebSocket数据流阻塞问题
基于问题分析.md和问题分析2.md的深度分析结果

核心诊断目标：
1. 配置系统混乱问题 - 多配置源不一致、单位转换错误
2. 架构过度复杂问题 - 6层处理导致数据老化和队列阻塞
3. 连接管理问题 - 频繁重连导致的连接风暴
4. 时间戳老化问题 - 数据在队列中等待30-60秒
"""

import os
import sys
import json
import time
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@dataclass
class ConfigurationIssue:
    """配置问题"""
    config_file: str
    parameter: str
    value: Any
    unit: str
    expected_value: Any
    expected_unit: str
    severity: str
    description: str

@dataclass
class ArchitectureIssue:
    """架构问题"""
    component: str
    issue_type: str  # "redundant", "bottleneck", "complexity"
    description: str
    impact: str
    processing_layers: int
    severity: str

class PreciseGateOKXDiagnosis:
    """Gate.io和OKX数据阻塞精确诊断器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "123" / "config"
        self.websocket_dir = self.project_root / "123" / "websocket"
        self.logs_dir = self.project_root / "123" / "logs"
        
        self.configuration_issues: List[ConfigurationIssue] = []
        self.architecture_issues: List[ArchitectureIssue] = []
        self.recommendations: List[str] = []
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面诊断"""
        print("🔥 开始Gate.io和OKX数据阻塞精确诊断...")
        start_time = time.time()
        
        # 1. 诊断配置系统混乱问题
        self._diagnose_configuration_chaos()
        
        # 2. 诊断架构过度复杂问题
        self._diagnose_architecture_complexity()
        
        # 3. 诊断连接管理问题
        self._diagnose_connection_management()
        
        # 4. 诊断时间戳老化问题
        self._diagnose_timestamp_aging()
        
        # 5. 分析日志中的阻塞模式
        self._analyze_blocking_patterns()
        
        # 6. 生成修复建议
        self._generate_fix_recommendations()
        
        duration = time.time() - start_time
        
        # 生成诊断报告
        result = {
            "diagnosis_timestamp": datetime.now().isoformat(),
            "duration_seconds": round(duration, 2),
            "configuration_issues": [asdict(issue) for issue in self.configuration_issues],
            "architecture_issues": [asdict(issue) for issue in self.architecture_issues],
            "total_issues": len(self.configuration_issues) + len(self.architecture_issues),
            "critical_issues": len([i for i in self.configuration_issues + self.architecture_issues if i.severity == "CRITICAL"]),
            "recommendations": self.recommendations,
            "summary": self._generate_summary()
        }
        
        # 保存诊断结果
        self._save_diagnosis_result(result)
        
        return result
    
    def _diagnose_configuration_chaos(self):
        """诊断配置系统混乱问题"""
        print("🔍 诊断配置系统混乱问题...")
        
        # 检查settings.py中的配置
        settings_file = self.config_dir / "settings.py"
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings_content = f.read()
                
            # 检查ws_connect_timeout配置
            if "ws_connect_timeout=int(os.getenv('WS_CONNECT_TIMEOUT', '1000'))" in settings_content:
                self.configuration_issues.append(ConfigurationIssue(
                    config_file="settings.py",
                    parameter="ws_connect_timeout",
                    value=1000,
                    unit="毫秒",
                    expected_value=10,
                    expected_unit="秒",
                    severity="CRITICAL",
                    description="settings.py中ws_connect_timeout使用毫秒单位(1000ms)，与其他配置文件不一致"
                ))
        
        # 检查network_config.py中的配置
        network_config_file = self.config_dir / "network_config.py"
        if network_config_file.exists():
            with open(network_config_file, 'r', encoding='utf-8') as f:
                network_content = f.read()
                
            if "ws_connect_timeout: int = 10" in network_content:
                self.configuration_issues.append(ConfigurationIssue(
                    config_file="network_config.py",
                    parameter="ws_connect_timeout",
                    value=10,
                    unit="秒",
                    expected_value=10,
                    expected_unit="秒",
                    severity="MEDIUM",
                    description="network_config.py中ws_connect_timeout使用秒单位，与settings.py不一致"
                ))
        
        # 检查.env.sample中的配置
        env_sample_file = self.project_root / "123" / ".env.sample"
        if env_sample_file.exists():
            with open(env_sample_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
                
            if "WS_CONNECTION_TIMEOUT=10" in env_content:
                self.configuration_issues.append(ConfigurationIssue(
                    config_file=".env.sample",
                    parameter="WS_CONNECTION_TIMEOUT",
                    value=10,
                    unit="秒",
                    expected_value=10,
                    expected_unit="秒",
                    severity="MEDIUM",
                    description=".env.sample中WS_CONNECTION_TIMEOUT使用秒单位，参数名与其他文件不一致"
                ))
        
        # 检查单位转换错误
        ws_client_file = self.websocket_dir / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                ws_client_content = f.read()
                
            if "/ 1000.0" in ws_client_content and "connect_warning_threshold" in ws_client_content:
                self.configuration_issues.append(ConfigurationIssue(
                    config_file="ws_client.py",
                    parameter="connect_warning_threshold",
                    value="ws_connect_timeout / 1000.0",
                    unit="错误转换",
                    expected_value="正确的单位转换",
                    expected_unit="秒",
                    severity="CRITICAL",
                    description="ws_client.py中单位转换错误，如果ws_connect_timeout是秒，除以1000会得到毫秒级阈值"
                ))
    
    def _diagnose_architecture_complexity(self):
        """诊断架构过度复杂问题"""
        print("🔍 诊断架构过度复杂问题...")
        
        # 分析WebSocket数据处理层级
        processing_layers = [
            "WebSocket接收",
            "消息队列入队", 
            "消息分发器",
            "数据处理器",
            "锁管理器",
            "差价计算器"
        ]
        
        self.architecture_issues.append(ArchitectureIssue(
            component="WebSocket数据处理链路",
            issue_type="complexity",
            description="数据处理链路过长，包含6个处理层级",
            impact="数据在处理过程中老化，导致30-60秒延迟",
            processing_layers=len(processing_layers),
            severity="CRITICAL"
        ))
        
        # 检查消息队列瓶颈
        if (self.websocket_dir / "ws_client.py").exists():
            self.architecture_issues.append(ArchitectureIssue(
                component="消息队列",
                issue_type="bottleneck",
                description="消息队列成为处理瓶颈，数据排队等待处理",
                impact="高频数据在队列中积压，导致处理延迟",
                processing_layers=1,
                severity="HIGH"
            ))
        
        # 检查重复功能
        redundant_components = []
        if (self.websocket_dir / "unified_connection_pool_manager.py").exists():
            redundant_components.append("统一连接池管理器")
        if (self.websocket_dir / "gate_ws.py").exists():
            redundant_components.append("Gate专用WebSocket客户端")
        if (self.websocket_dir / "okx_ws.py").exists():
            redundant_components.append("OKX专用WebSocket客户端")
            
        if len(redundant_components) > 1:
            self.architecture_issues.append(ArchitectureIssue(
                component="连接管理",
                issue_type="redundant",
                description=f"存在重复的连接管理组件: {', '.join(redundant_components)}",
                impact="功能重复导致维护困难和潜在冲突",
                processing_layers=len(redundant_components),
                severity="MEDIUM"
            ))
    
    def _diagnose_connection_management(self):
        """诊断连接管理问题"""
        print("🔍 诊断连接管理问题...")
        
        # 检查频繁重连问题
        if self.configuration_issues:
            # 如果存在配置问题，很可能导致频繁重连
            self.architecture_issues.append(ArchitectureIssue(
                component="连接重连机制",
                issue_type="bottleneck",
                description="配置错误导致连接超时阈值过小，引发频繁重连",
                impact="连接风暴导致数据流中断和阻塞",
                processing_layers=1,
                severity="HIGH"
            ))
    
    def _diagnose_timestamp_aging(self):
        """诊断时间戳老化问题"""
        print("🔍 诊断时间戳老化问题...")
        
        # 检查时间戳处理逻辑
        ws_client_file = self.websocket_dir / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "receive_timestamp_ms" in content and "_message_queue" in content:
                self.architecture_issues.append(ArchitectureIssue(
                    component="时间戳处理",
                    issue_type="bottleneck",
                    description="数据在消息队列中等待处理时，时间戳逐渐老化",
                    impact="老化的时间戳被误判为过期数据，导致数据丢弃",
                    processing_layers=1,
                    severity="HIGH"
                ))
    
    def _analyze_blocking_patterns(self):
        """分析日志中的阻塞模式"""
        print("🔍 分析日志中的阻塞模式...")
        
        # 检查阻塞日志文件
        blocking_log = self.logs_dir / "websocket_blocking_20250805.log"
        if blocking_log.exists():
            try:
                with open(blocking_log, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 分析频率下降模式
                if "频率=" in log_content:
                    lines = log_content.split('\n')
                    gate_frequencies = []
                    okx_frequencies = []
                    
                    for line in lines:
                        if "gate_spot" in line and "频率=" in line:
                            try:
                                freq_str = line.split("频率=")[1].split("/s")[0]
                                freq = float(freq_str)
                                gate_frequencies.append(freq)
                            except:
                                continue
                        elif "okx_spot" in line and "频率=" in line:
                            try:
                                freq_str = line.split("频率=")[1].split("/s")[0]
                                freq = float(freq_str)
                                okx_frequencies.append(freq)
                            except:
                                continue
                    
                    # 分析频率下降趋势
                    if gate_frequencies and len(gate_frequencies) > 10:
                        initial_freq = sum(gate_frequencies[:5]) / 5
                        final_freq = sum(gate_frequencies[-5:]) / 5
                        if initial_freq > final_freq * 2:  # 频率下降超过50%
                            self.architecture_issues.append(ArchitectureIssue(
                                component="Gate.io数据流",
                                issue_type="bottleneck",
                                description=f"Gate.io数据频率从{initial_freq:.1f}/s下降到{final_freq:.1f}/s",
                                impact="数据流逐渐阻塞，最终导致40000ms+延迟",
                                processing_layers=1,
                                severity="CRITICAL"
                            ))
                    
                    if okx_frequencies and len(okx_frequencies) > 10:
                        initial_freq = sum(okx_frequencies[:5]) / 5
                        final_freq = sum(okx_frequencies[-5:]) / 5
                        if initial_freq > final_freq * 2:
                            self.architecture_issues.append(ArchitectureIssue(
                                component="OKX数据流",
                                issue_type="bottleneck", 
                                description=f"OKX数据频率从{initial_freq:.1f}/s下降到{final_freq:.1f}/s",
                                impact="数据流逐渐阻塞，最终导致40000ms+延迟",
                                processing_layers=1,
                                severity="CRITICAL"
                            ))
                            
            except Exception as e:
                self.logger.warning(f"分析阻塞日志失败: {e}")
    
    def _generate_fix_recommendations(self):
        """生成修复建议"""
        print("🔥 生成修复建议...")
        
        # 基于配置问题的建议
        if any(issue.severity == "CRITICAL" for issue in self.configuration_issues):
            self.recommendations.append(
                "🔥 CRITICAL: 立即统一WebSocket连接超时配置，所有配置文件使用相同单位(秒)和数值"
            )
            self.recommendations.append(
                "🔥 CRITICAL: 修复ws_client.py中的单位转换错误，移除错误的/1000.0计算"
            )
        
        # 基于架构问题的建议
        if any(issue.issue_type == "complexity" for issue in self.architecture_issues):
            self.recommendations.append(
                "🔥 HIGH: 简化WebSocket数据处理架构，从6层减少到2-3层"
            )
            self.recommendations.append(
                "🔥 HIGH: 移除消息队列瓶颈，实现WebSocket数据直接传递给差价计算器"
            )
        
        if any(issue.issue_type == "redundant" for issue in self.architecture_issues):
            self.recommendations.append(
                "🔥 MEDIUM: 整合重复的连接管理组件，使用统一的连接池管理器"
            )
        
        # 基于阻塞模式的建议
        if any(issue.component in ["Gate.io数据流", "OKX数据流"] for issue in self.architecture_issues):
            self.recommendations.append(
                "🔥 CRITICAL: 优化Gate.io和OKX的数据处理逻辑，防止频率下降导致的阻塞"
            )
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成诊断摘要"""
        return {
            "total_configuration_issues": len(self.configuration_issues),
            "total_architecture_issues": len(self.architecture_issues),
            "critical_issues": len([i for i in self.configuration_issues + self.architecture_issues if i.severity == "CRITICAL"]),
            "high_issues": len([i for i in self.configuration_issues + self.architecture_issues if i.severity == "HIGH"]),
            "medium_issues": len([i for i in self.configuration_issues + self.architecture_issues if i.severity == "MEDIUM"]),
            "primary_root_causes": [
                "配置系统混乱 - 多配置源不一致",
                "架构过度复杂 - 6层处理导致延迟",
                "连接管理问题 - 频繁重连风暴",
                "时间戳老化 - 数据在队列中老化"
            ]
        }
    
    def _save_diagnosis_result(self, result: Dict[str, Any]):
        """保存诊断结果"""
        output_file = self.project_root / "123" / "logs" / f"precise_diagnosis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"📊 诊断结果已保存到: {output_file}")

def main():
    """主函数"""
    diagnosis = PreciseGateOKXDiagnosis()
    result = diagnosis.run_comprehensive_diagnosis()
    
    print("\n" + "="*80)
    print("🔥 Gate.io和OKX数据阻塞精确诊断结果")
    print("="*80)
    
    print(f"📊 诊断耗时: {result['duration_seconds']}秒")
    print(f"🔍 发现问题总数: {result['total_issues']}")
    print(f"🚨 严重问题数: {result['critical_issues']}")
    
    print("\n🔥 配置问题:")
    for issue in result['configuration_issues']:
        print(f"  [{issue['severity']}] {issue['config_file']}: {issue['description']}")
    
    print("\n🔥 架构问题:")
    for issue in result['architecture_issues']:
        print(f"  [{issue['severity']}] {issue['component']}: {issue['description']}")
    
    print("\n🔥 修复建议:")
    for i, rec in enumerate(result['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    print("\n" + "="*80)
    print("🎯 结论: Gate和OKX数据阻塞的根本原因是配置混乱和架构过度复杂！")
    print("="*80)
    
    return result

if __name__ == "__main__":
    main()
