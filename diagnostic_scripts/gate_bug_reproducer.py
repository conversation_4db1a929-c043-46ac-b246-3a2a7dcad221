#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚨 Gate.io数据阻塞问题专项BUG复现脚本

根据深度审查发现的关键问题：
1. Gate.io订阅格式与Bybit/OKX不一致，可能导致数据处理延迟
2. Gate.io使用单个订阅方式，而其他交易所使用批量订阅
3. Gate.io的时间戳提取逻辑存在双重检查，可能影响性能
4. Gate.io的订阅频率控制可能不当

目标：精确复现5649ms数据延迟问题
"""

import asyncio
import websockets
import json
import time
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目路径
sys.path.append("/root/myproject/123/70 gate和okx还是数据阻塞/123")

class GateBugReproducer:
    """Gate.io BUG复现器"""
    
    def __init__(self):
        self.gate_ws_url = "wss://api.gateio.ws/ws/v4/"
        self.test_symbols = ["BTC_USDT", "ETH_USDT", "ADA_USDT"]
        self.received_messages = []
        self.timestamp_delays = []
        self.subscription_results = {}
        
    async def run_comprehensive_bug_reproduction(self):
        """运行完整的BUG复现测试"""
        print("🚨 Gate.io数据阻塞问题专项BUG复现开始")
        print("=" * 70)
        
        # 1. 测试当前订阅格式
        await self.test_current_subscription_format()
        
        # 2. 测试优化订阅格式
        await self.test_optimized_subscription_format()
        
        # 3. 时间戳延迟分析
        await self.analyze_timestamp_delays()
        
        # 4. 生成BUG报告
        self.generate_bug_report()
        
    async def test_current_subscription_format(self):
        """测试当前的订阅格式（存在问题的版本）"""
        print("\n🔍 1. 测试当前订阅格式（可能存在问题）")
        print("-" * 50)
        
        try:
            async with websockets.connect(self.gate_ws_url) as websocket:
                print(f"✅ WebSocket连接成功: {self.gate_ws_url}")
                
                # 使用当前代码中的订阅格式（可能有问题）
                for symbol in self.test_symbols:
                    subscription_msg = {
                        "time": int(time.time()),
                        "channel": "spot.order_book",
                        "event": "subscribe",
                        "payload": [symbol, "50", "100ms"]  # 当前使用的格式
                    }
                    
                    print(f"📤 发送订阅: {symbol} -> {json.dumps(subscription_msg)}")
                    await websocket.send(json.dumps(subscription_msg))
                    
                    # 等待订阅响应
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        
                        print(f"📥 订阅响应: {json.dumps(response_data, indent=2)}")
                        
                        # 记录订阅结果
                        self.subscription_results[f"current_{symbol}"] = {
                            "success": response_data.get("result", {}).get("status") == "success",
                            "response": response_data,
                            "timestamp": time.time()
                        }
                        
                    except asyncio.TimeoutError:
                        print(f"⚠️ 订阅响应超时: {symbol}")
                        self.subscription_results[f"current_{symbol}"] = {
                            "success": False,
                            "error": "timeout",
                            "timestamp": time.time()
                        }
                    
                    # 控制订阅频率
                    await asyncio.sleep(0.1)
                
                # 收集数据10秒
                print(f"\n📊 收集数据10秒...")
                start_time = time.time()
                message_count = 0
                
                while time.time() - start_time < 10:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        message_data = json.loads(message)
                        
                        message_count += 1
                        receive_time = time.time() * 1000
                        
                        # 分析时间戳延迟
                        await self._analyze_message_timestamp(message_data, receive_time, "current")
                        
                        if message_count <= 5:  # 只打印前5条消息
                            print(f"📨 消息 {message_count}: {json.dumps(message_data)[:200]}...")
                            
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        print(f"⚠️ 消息处理错误: {e}")
                        
                print(f"✅ 当前格式测试完成，收到 {message_count} 条消息")
                
        except Exception as e:
            print(f"❌ 当前格式测试失败: {e}")
            
    async def test_optimized_subscription_format(self):
        """测试优化的订阅格式"""
        print("\n🔧 2. 测试优化订阅格式")
        print("-" * 50)
        
        try:
            async with websockets.connect(self.gate_ws_url) as websocket:
                print(f"✅ WebSocket连接成功: {self.gate_ws_url}")
                
                # 测试官方文档推荐的格式
                for symbol in self.test_symbols:
                    # 格式1：使用book_ticker（最快10ms更新）
                    subscription_msg = {
                        "time": int(time.time()),
                        "channel": "spot.book_ticker",
                        "event": "subscribe",
                        "payload": [symbol]
                    }
                    
                    print(f"📤 发送优化订阅: {symbol} -> {json.dumps(subscription_msg)}")
                    await websocket.send(json.dumps(subscription_msg))
                    
                    # 等待订阅响应
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        
                        print(f"📥 优化订阅响应: {json.dumps(response_data, indent=2)}")
                        
                        # 记录订阅结果
                        self.subscription_results[f"optimized_{symbol}"] = {
                            "success": response_data.get("result", {}).get("status") == "success",
                            "response": response_data,
                            "timestamp": time.time()
                        }
                        
                    except asyncio.TimeoutError:
                        print(f"⚠️ 优化订阅响应超时: {symbol}")
                        self.subscription_results[f"optimized_{symbol}"] = {
                            "success": False,
                            "error": "timeout",
                            "timestamp": time.time()
                        }
                    
                    await asyncio.sleep(0.1)
                
                # 收集数据10秒
                print(f"\n📊 收集优化格式数据10秒...")
                start_time = time.time()
                message_count = 0
                
                while time.time() - start_time < 10:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        message_data = json.loads(message)
                        
                        message_count += 1
                        receive_time = time.time() * 1000
                        
                        # 分析时间戳延迟
                        await self._analyze_message_timestamp(message_data, receive_time, "optimized")
                        
                        if message_count <= 5:  # 只打印前5条消息
                            print(f"📨 优化消息 {message_count}: {json.dumps(message_data)[:200]}...")
                            
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        print(f"⚠️ 优化消息处理错误: {e}")
                        
                print(f"✅ 优化格式测试完成，收到 {message_count} 条消息")
                
        except Exception as e:
            print(f"❌ 优化格式测试失败: {e}")
            
    async def _analyze_message_timestamp(self, message_data: Dict[str, Any], receive_time: float, test_type: str):
        """分析消息时间戳延迟"""
        try:
            extracted_timestamp = None
            extraction_source = "unknown"
            
            # 模拟当前系统的时间戳提取逻辑
            if 't' in message_data:
                extracted_timestamp = message_data['t']
                extraction_source = "gate_t_field"
            elif 'result' in message_data and isinstance(message_data['result'], dict):
                result_data = message_data['result']
                if 't' in result_data:
                    extracted_timestamp = result_data['t']
                    extraction_source = "gate_result_t_field"
                    
            if extracted_timestamp:
                if isinstance(extracted_timestamp, (int, float)):
                    extracted_timestamp = int(extracted_timestamp)
                else:
                    extracted_timestamp = int(float(extracted_timestamp))
                    
                # 计算延迟
                delay_ms = receive_time - extracted_timestamp
                
                self.timestamp_delays.append({
                    'test_type': test_type,
                    'delay_ms': delay_ms,
                    'extraction_source': extraction_source,
                    'extracted_timestamp': extracted_timestamp,
                    'receive_time': receive_time,
                    'message_type': message_data.get('channel', 'unknown')
                })
                
                # 如果延迟超过1000ms，记录详细信息
                if delay_ms > 1000:
                    print(f"🚨 检测到延迟: {delay_ms:.1f}ms > 1000ms")
                    print(f"   测试类型: {test_type}")
                    print(f"   提取来源: {extraction_source}")
                    print(f"   消息类型: {message_data.get('channel', 'unknown')}")
                    
        except Exception as e:
            print(f"⚠️ 时间戳分析错误: {e}")
            
    async def analyze_timestamp_delays(self):
        """分析时间戳延迟统计"""
        print("\n📈 3. 时间戳延迟分析")
        print("-" * 50)
        
        if not self.timestamp_delays:
            print("⚠️ 没有收集到时间戳数据")
            return
            
        # 按测试类型分组分析
        current_delays = [d['delay_ms'] for d in self.timestamp_delays if d['test_type'] == 'current']
        optimized_delays = [d['delay_ms'] for d in self.timestamp_delays if d['test_type'] == 'optimized']
        
        print(f"📊 统计结果:")
        print(f"当前格式样本数: {len(current_delays)}")
        print(f"优化格式样本数: {len(optimized_delays)}")
        
        if current_delays:
            current_avg = sum(current_delays) / len(current_delays)
            current_max = max(current_delays)
            current_over_threshold = len([d for d in current_delays if d > 1000])
            
            print(f"\n🔍 当前格式分析:")
            print(f"   平均延迟: {current_avg:.1f}ms")
            print(f"   最大延迟: {current_max:.1f}ms")
            print(f"   超过1000ms阈值: {current_over_threshold}/{len(current_delays)} ({current_over_threshold/len(current_delays)*100:.1f}%)")
            
        if optimized_delays:
            optimized_avg = sum(optimized_delays) / len(optimized_delays)
            optimized_max = max(optimized_delays)
            optimized_over_threshold = len([d for d in optimized_delays if d > 1000])
            
            print(f"\n🔧 优化格式分析:")
            print(f"   平均延迟: {optimized_avg:.1f}ms")
            print(f"   最大延迟: {optimized_max:.1f}ms")
            print(f"   超过1000ms阈值: {optimized_over_threshold}/{len(optimized_delays)} ({optimized_over_threshold/len(optimized_delays)*100:.1f}%)")
            
            # 对比分析
            if current_delays and optimized_delays:
                improvement = current_avg - optimized_avg
                print(f"\n💡 改进效果:")
                print(f"   平均延迟改进: {improvement:.1f}ms")
                print(f"   改进百分比: {improvement/current_avg*100:.1f}%")
                
    def generate_bug_report(self):
        """生成BUG报告"""
        print("\n📋 4. BUG报告生成")
        print("=" * 70)
        
        report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"🚨 Gate.io数据阻塞BUG专项报告")
        print(f"生成时间: {report_time}")
        
        # 订阅成功率分析
        current_success = len([k for k, v in self.subscription_results.items() if k.startswith('current_') and v.get('success', False)])
        optimized_success = len([k for k, v in self.subscription_results.items() if k.startswith('optimized_') and v.get('success', False)])
        
        print(f"\n📊 订阅成功率:")
        print(f"当前格式: {current_success}/{len(self.test_symbols)} ({current_success/len(self.test_symbols)*100:.0f}%)")
        print(f"优化格式: {optimized_success}/{len(self.test_symbols)} ({optimized_success/len(self.test_symbols)*100:.0f}%)")
        
        # 延迟问题分析
        high_delay_count = len([d for d in self.timestamp_delays if d['delay_ms'] > 1000])
        total_messages = len(self.timestamp_delays)
        
        print(f"\n🚨 延迟问题:")
        print(f"总消息数: {total_messages}")
        print(f"高延迟消息(>1000ms): {high_delay_count}")
        print(f"高延迟比例: {high_delay_count/total_messages*100:.1f}%" if total_messages > 0 else "无数据")
        
        # 根本原因分析
        print(f"\n🔍 BUG根本原因分析:")
        
        if current_success < len(self.test_symbols):
            print(f"1. 🚨 订阅格式问题：当前使用的[symbol, '50', '100ms']格式可能不被服务器完全支持")
            print(f"   - 建议使用spot.book_ticker频道获取最快更新")
            print(f"   - 或调整order_book的参数格式")
            
        if high_delay_count > 0:
            print(f"2. 🚨 时间戳处理问题：检测到{high_delay_count}条高延迟消息")
            print(f"   - 可能的原因：WebSocket连接质量")
            print(f"   - 可能的原因：消息处理逻辑效率")
            print(f"   - 可能的原因：订阅频道选择不当")
            
        # 修复建议
        print(f"\n💡 修复建议:")
        print(f"1. 更换订阅频道为spot.book_ticker（10ms更新频率）")
        print(f"2. 优化订阅参数格式")
        print(f"3. 添加WebSocket连接质量监控")
        print(f"4. 实施智能重连机制")
        
        # 保存详细报告
        try:
            report_file = f"/root/myproject/123/70 gate和okx还是数据阻塞/diagnostic_results/gate_bug_reproduction_{int(time.time())}.json"
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            detailed_report = {
                'report_time': report_time,
                'subscription_results': self.subscription_results,
                'timestamp_delays': self.timestamp_delays,
                'summary': {
                    'current_success_rate': current_success / len(self.test_symbols) if self.test_symbols else 0,
                    'optimized_success_rate': optimized_success / len(self.test_symbols) if self.test_symbols else 0,
                    'high_delay_count': high_delay_count,
                    'total_messages': total_messages,
                    'high_delay_percentage': high_delay_count/total_messages*100 if total_messages > 0 else 0
                }
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(detailed_report, f, indent=2, ensure_ascii=False)
                
            print(f"\n📄 详细报告已保存: {report_file}")
            
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")

async def main():
    """主函数"""
    reproducer = GateBugReproducer()
    await reproducer.run_comprehensive_bug_reproduction()

if __name__ == "__main__":
    asyncio.run(main())