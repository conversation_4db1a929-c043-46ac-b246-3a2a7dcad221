#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Gate.io和OKX数据阻塞问题精确诊断脚本
基于用户反馈的40000ms延迟问题和07B修复文档分析

诊断重点：
1. Gate期货数据缺失问题
2. OKX API频率限制问题  
3. WebSocket数据流阻塞问题
4. 时间戳延迟累积问题
"""

import json
import time
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class GateOKXBlockingDiagnosis:
    """Gate.io和OKX数据阻塞问题精确诊断器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.logs_dir = self.project_root / "123" / "logs"
        self.results = {
            "diagnosis_time": datetime.now().isoformat(),
            "issues_found": [],
            "severity_stats": {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0},
            "recommendations": []
        }
        
    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面诊断"""
        print("🔍 开始Gate.io和OKX数据阻塞问题诊断...")
        
        # 1. 诊断Gate期货数据缺失
        self._diagnose_gate_futures_missing()
        
        # 2. 诊断OKX API频率问题
        self._diagnose_okx_api_limits()
        
        # 3. 诊断WebSocket数据流阻塞
        self._diagnose_websocket_blocking()
        
        # 4. 分析延迟累积问题
        self._analyze_latency_accumulation()
        
        # 5. 生成综合建议
        self._generate_recommendations()
        
        return self.results
    
    def _diagnose_gate_futures_missing(self):
        """诊断Gate期货数据缺失问题"""
        try:
            # 检查websocket_prices.log中的套利组合
            prices_log = self.logs_dir / "websocket_prices.log"
            if not prices_log.exists():
                self._add_issue("CRITICAL", "websocket_prices_missing", 
                              "websocket_prices.log文件不存在，无法分析套利数据")
                return
                
            # 分析套利组合分布
            combo_stats = {"[A]": 0, "[B]": 0, "[C]": 0, "[D]": 0, "[E]": 0, "[F]": 0}
            
            with open(prices_log, 'r', encoding='utf-8') as f:
                for line in f:
                    for combo in combo_stats.keys():
                        if combo in line:
                            combo_stats[combo] += 1
            
            # 检查Gate期货相关组合
            gate_futures_combos = {"[B]": "Bybit现货 ↔ Gate期货", "[E]": "OKX现货 ↔ Gate期货"}
            missing_combos = []
            
            for combo, description in gate_futures_combos.items():
                if combo_stats[combo] == 0:
                    missing_combos.append(f"{combo} {description}")
            
            if missing_combos:
                self._add_issue("CRITICAL", "gate_futures_data_missing",
                              f"Gate期货数据完全缺失，缺失组合: {', '.join(missing_combos)}")
                self._add_issue("HIGH", "gate_futures_websocket_broken",
                              "Gate.io期货WebSocket数据流可能已断开或配置错误")
            else:
                print("✅ Gate期货数据正常")
                
        except Exception as e:
            self._add_issue("HIGH", "gate_diagnosis_error", f"Gate期货诊断异常: {e}")
    
    def _diagnose_okx_api_limits(self):
        """诊断OKX API频率限制问题"""
        try:
            error_log = self.logs_dir / "error_20250805.log"
            if not error_log.exists():
                print("⚠️ error_20250805.log不存在，跳过OKX API诊断")
                return
            
            okx_50011_count = 0
            okx_50011_times = []
            
            with open(error_log, 'r', encoding='utf-8') as f:
                for line in f:
                    if "50011" in line and "Too Many Requests" in line:
                        okx_50011_count += 1
                        # 提取时间戳
                        if line.startswith("2025-"):
                            time_part = line.split()[0] + " " + line.split()[1]
                            okx_50011_times.append(time_part)
            
            if okx_50011_count > 0:
                self._add_issue("CRITICAL", "okx_api_rate_limit",
                              f"OKX API频率超限，50011错误{okx_50011_count}次")
                self._add_issue("HIGH", "okx_api_config_error",
                              "OKX API调用频率配置超过官方限制，需要降低到1次/秒")
                
                if okx_50011_times:
                    self.results["okx_50011_timeline"] = okx_50011_times
            else:
                print("✅ OKX API频率正常")
                
        except Exception as e:
            self._add_issue("MEDIUM", "okx_diagnosis_error", f"OKX API诊断异常: {e}")
    
    def _diagnose_websocket_blocking(self):
        """诊断WebSocket数据流阻塞问题"""
        try:
            silent_log = self.logs_dir / "websocket_silent_disconnect_20250805.log"
            if not silent_log.exists():
                print("⚠️ websocket_silent_disconnect_20250805.log不存在，跳过WebSocket诊断")
                return
            
            blocking_events = {"gate": 0, "okx": 0, "bybit": 0}
            max_duration = {"gate": 0, "okx": 0, "bybit": 0}
            
            with open(silent_log, 'r', encoding='utf-8') as f:
                for line in f:
                    if "检测到数据流阻塞" in line:
                        for exchange in blocking_events.keys():
                            if f"[{exchange}]" in line:
                                blocking_events[exchange] += 1
                                # 提取阻塞时长
                                if "silent_duration_seconds" in line:
                                    try:
                                        duration_str = line.split("'silent_duration_seconds': ")[1].split(",")[0]
                                        duration = float(duration_str)
                                        max_duration[exchange] = max(max_duration[exchange], duration)
                                    except:
                                        pass
            
            # 分析阻塞严重程度
            for exchange, count in blocking_events.items():
                if count > 10:
                    severity = "CRITICAL"
                elif count > 5:
                    severity = "HIGH"
                elif count > 0:
                    severity = "MEDIUM"
                else:
                    continue
                    
                self._add_issue(severity, f"{exchange}_websocket_blocking",
                              f"{exchange.upper()} WebSocket数据流阻塞{count}次，最长{max_duration[exchange]:.1f}秒")
            
            # 检查是否存在长时间阻塞（40秒+）
            for exchange, duration in max_duration.items():
                if duration >= 40:
                    self._add_issue("CRITICAL", f"{exchange}_extreme_blocking",
                                  f"{exchange.upper()}极端阻塞{duration:.1f}秒，可能导致40000ms+延迟")
                                  
        except Exception as e:
            self._add_issue("MEDIUM", "websocket_diagnosis_error", f"WebSocket诊断异常: {e}")
    
    def _analyze_latency_accumulation(self):
        """分析延迟累积问题"""
        try:
            # 检查是否存在时间戳处理问题
            if any(issue["type"] in ["okx_api_rate_limit", "gate_websocket_blocking", "okx_websocket_blocking"] 
                   for issue in self.results["issues_found"]):
                
                self._add_issue("CRITICAL", "latency_accumulation",
                              "API限频 + WebSocket阻塞导致延迟累积，可能达到40000ms+")
                              
                # 分析延迟产生链路
                latency_chain = [
                    "1. OKX API频率超限 (50011错误)",
                    "2. WebSocket连接被服务器拒绝 (HTTP 503)",
                    "3. 数据流长时间阻塞 (30-60秒)",
                    "4. 用户感知极端延迟 (40000ms+)"
                ]
                
                self.results["latency_chain_analysis"] = latency_chain
                
        except Exception as e:
            self._add_issue("LOW", "latency_analysis_error", f"延迟分析异常: {e}")
    
    def _generate_recommendations(self):
        """生成修复建议"""
        recommendations = []
        
        # 基于发现的问题生成建议
        issue_types = [issue["type"] for issue in self.results["issues_found"]]
        
        if "gate_futures_data_missing" in issue_types:
            recommendations.append({
                "priority": "CRITICAL",
                "title": "修复Gate期货WebSocket数据流",
                "actions": [
                    "检查Gate.io期货WebSocket订阅配置",
                    "验证Gate期货交易对在交易所是否存在",
                    "检查Gate期货WebSocket认证和连接状态"
                ]
            })
        
        if "okx_api_rate_limit" in issue_types:
            recommendations.append({
                "priority": "CRITICAL", 
                "title": "降低OKX API调用频率",
                "actions": [
                    "将OKX rate_limit从2次/秒降至1次/秒",
                    "增加API调用间隔到1.5秒",
                    "实施指数退避重试机制"
                ]
            })
        
        if any("websocket_blocking" in t for t in issue_types):
            recommendations.append({
                "priority": "HIGH",
                "title": "优化WebSocket连接稳定性", 
                "actions": [
                    "实施WebSocket连接健康检查",
                    "添加自动重连机制",
                    "优化心跳间隔配置"
                ]
            })
        
        self.results["recommendations"] = recommendations
    
    def _add_issue(self, severity: str, issue_type: str, description: str):
        """添加问题到结果"""
        self.results["issues_found"].append({
            "severity": severity,
            "type": issue_type,
            "description": description,
            "timestamp": datetime.now().isoformat()
        })
        self.results["severity_stats"][severity] += 1
        
        # 输出到控制台
        severity_emoji = {"CRITICAL": "🚨", "HIGH": "⚠️", "MEDIUM": "📋", "LOW": "ℹ️"}
        print(f"{severity_emoji.get(severity, '📋')} [{severity}] {description}")
    
    def save_results(self, filename: str = None):
        """保存诊断结果"""
        if filename is None:
            timestamp = int(time.time())
            filename = f"gate_okx_blocking_diagnosis_{timestamp}.json"
        
        results_file = self.project_root / "diagnostic_results" / filename
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 诊断结果已保存到: {results_file}")
        return results_file


def main():
    """主函数"""
    print("🚀 Gate.io和OKX数据阻塞问题精确诊断脚本")
    print("=" * 60)
    
    diagnosis = GateOKXBlockingDiagnosis()
    results = diagnosis.run_comprehensive_diagnosis()
    
    # 输出统计信息
    print("\n📊 诊断统计:")
    for severity, count in results["severity_stats"].items():
        if count > 0:
            print(f"   {severity}: {count}个问题")
    
    print(f"\n🎯 总计发现 {len(results['issues_found'])} 个问题")
    
    # 保存结果
    results_file = diagnosis.save_results()
    
    # 输出关键建议
    if results["recommendations"]:
        print("\n🛠️ 关键修复建议:")
        for i, rec in enumerate(results["recommendations"], 1):
            print(f"   {i}. [{rec['priority']}] {rec['title']}")
    
    print("\n✅ 诊断完成!")
    return results_file


if __name__ == "__main__":
    main()