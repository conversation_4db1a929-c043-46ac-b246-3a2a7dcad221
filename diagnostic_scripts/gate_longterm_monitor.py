#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Gate.io长期监控脚本 - 专门捕获5649ms延迟问题

目标：长期运行监控，捕获真正的数据阻塞问题
"""

import asyncio
import time
import json
import sys
import os
from datetime import datetime

sys.path.append("/root/myproject/123/70 gate和okx还是数据阻塞/123")

class GateLongTermMonitor:
    """Gate.io长期监控器"""
    
    def __init__(self):
        self.high_delay_threshold = 1000  # 1000ms阈值
        self.extreme_delay_threshold = 5000  # 5000ms极端延迟
        self.high_delay_events = []
        self.total_messages = 0
        self.start_time = time.time()
        
    async def run_long_term_monitoring(self, duration_minutes=30):
        """运行长期监控"""
        print(f"🔍 开始Gate.io长期监控 - {duration_minutes}分钟")
        print("专门捕获5649ms类型的数据延迟问题")
        print("=" * 60)
        
        try:
            # 导入Gate.io WebSocket客户端
            from websocket.gate_ws import GateWebSocketClient
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 创建客户端实例
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT", "ETH_USDT", "ADA_USDT"])
            
            # 监听数据事件
            client.on("market_data", self.on_market_data)
            
            print(f"✅ 启动Gate.io WebSocket监控...")
            
            # 启动客户端
            client_task = asyncio.create_task(client.run())
            
            # 监控指定时间
            end_time = time.time() + (duration_minutes * 60)
            
            while time.time() < end_time:
                await asyncio.sleep(10)  # 每10秒输出一次状态
                self.print_monitoring_status()
                
            # 停止客户端
            client.running = False
            await client_task
            
            # 生成最终报告
            self.generate_final_report()
            
        except Exception as e:
            print(f"❌ 监控过程出错: {e}")
            import traceback
            traceback.print_exc()
            
    def on_market_data(self, data):
        """处理市场数据事件"""
        try:
            self.total_messages += 1
            receive_time = time.time() * 1000
            
            # 提取时间戳
            timestamp = data.get('timestamp')
            if timestamp:
                delay_ms = receive_time - timestamp
                
                # 检查高延迟
                if delay_ms > self.high_delay_threshold:
                    event = {
                        'timestamp': timestamp,
                        'receive_time': receive_time,
                        'delay_ms': delay_ms,
                        'symbol': data.get('symbol', 'unknown'),
                        'exchange': data.get('exchange', 'unknown'),
                        'event_time': datetime.now().isoformat(),
                        'is_extreme': delay_ms > self.extreme_delay_threshold
                    }
                    
                    self.high_delay_events.append(event)
                    
                    # 立即打印极端延迟事件
                    if delay_ms > self.extreme_delay_threshold:
                        print(f"\n🚨 检测到极端延迟事件!")
                        print(f"   时间: {datetime.now().strftime('%H:%M:%S')}")
                        print(f"   延迟: {delay_ms:.1f}ms")
                        print(f"   交易对: {data.get('symbol', 'unknown')}")
                        print(f"   原始时间戳: {timestamp}")
                        print(f"   接收时间戳: {receive_time}")
                        
                        # 特别处理5000-6000ms范围的延迟（接近问题日志中的5649ms）
                        if 5000 <= delay_ms <= 6000:
                            print(f"   ⚠️ 这接近日志中的5649ms问题!")
                            
        except Exception as e:
            print(f"⚠️ 数据处理错误: {e}")
            
    def print_monitoring_status(self):
        """打印监控状态"""
        current_time = datetime.now().strftime("%H:%M:%S")
        runtime_minutes = (time.time() - self.start_time) / 60
        
        high_delay_count = len(self.high_delay_events)
        extreme_delay_count = len([e for e in self.high_delay_events if e['is_extreme']])
        
        print(f"[{current_time}] 运行{runtime_minutes:.1f}分钟 | "
              f"总消息:{self.total_messages} | "
              f"高延迟:{high_delay_count} | "
              f"极端延迟:{extreme_delay_count}")
              
        # 如果有新的高延迟事件，显示最近的
        if high_delay_count > 0:
            recent_event = self.high_delay_events[-1]
            print(f"   最近延迟: {recent_event['delay_ms']:.1f}ms ({recent_event['symbol']})")
            
    def generate_final_report(self):
        """生成最终报告"""
        print(f"\n📋 Gate.io长期监控最终报告")
        print("=" * 60)
        
        runtime_minutes = (time.time() - self.start_time) / 60
        high_delay_count = len(self.high_delay_events)
        extreme_delay_count = len([e for e in self.high_delay_events if e['is_extreme']])
        
        print(f"监控时长: {runtime_minutes:.1f}分钟")
        print(f"总处理消息: {self.total_messages}")
        print(f"高延迟事件(>1000ms): {high_delay_count}")
        print(f"极端延迟事件(>5000ms): {extreme_delay_count}")
        
        if self.total_messages > 0:
            high_delay_rate = (high_delay_count / self.total_messages) * 100
            print(f"高延迟率: {high_delay_rate:.2f}%")
            
        if high_delay_count > 0:
            delays = [e['delay_ms'] for e in self.high_delay_events]
            avg_delay = sum(delays) / len(delays)
            max_delay = max(delays)
            
            print(f"\n🚨 延迟统计:")
            print(f"   平均高延迟: {avg_delay:.1f}ms")
            print(f"   最大延迟: {max_delay:.1f}ms")
            
            # 分析5000-6000ms范围的事件（接近5649ms问题）
            target_range_events = [e for e in self.high_delay_events if 5000 <= e['delay_ms'] <= 6000]
            if target_range_events:
                print(f"\n🎯 发现{len(target_range_events)}个接近5649ms的延迟事件:")
                for event in target_range_events:
                    print(f"   - {event['delay_ms']:.1f}ms @ {event['event_time']} ({event['symbol']})")
        else:
            print(f"\n✅ 未检测到高延迟事件")
            print(f"   可能原因: 1) 网络状况良好 2) 问题是间歇性的 3) 问题已被修复")
            
        # 保存详细报告
        try:
            report_file = f"/root/myproject/123/70 gate和okx还是数据阻塞/diagnostic_results/gate_longterm_monitor_{int(time.time())}.json"
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            report_data = {
                'monitoring_duration_minutes': runtime_minutes,
                'total_messages': self.total_messages,
                'high_delay_events': self.high_delay_events,
                'summary': {
                    'high_delay_count': high_delay_count,
                    'extreme_delay_count': extreme_delay_count,
                    'high_delay_rate_percent': (high_delay_count / self.total_messages) * 100 if self.total_messages > 0 else 0
                }
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
                
            print(f"\n📄 详细报告已保存: {report_file}")
            
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")

async def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description='Gate.io长期监控')
    parser.add_argument('--minutes', type=int, default=30, help='监控时长(分钟)')
    args = parser.parse_args()
    
    monitor = GateLongTermMonitor()
    await monitor.run_long_term_monitoring(args.minutes)

if __name__ == "__main__":
    asyncio.run(main())