#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Gate期货数据格式解析错误动态测试脚本
根据用户发现的关键问题：Gate快照数据不完整 asks=0 bids=0

测试重点：
1. Gate期货WebSocket消息格式解析
2. asks/bids数据提取逻辑
3. 数据格式转换过程
4. market_data事件发送机制
"""

import asyncio
import json
import time
import websockets
from datetime import datetime
from typing import Dict, List, Any

class GateFuturesParsingDebugger:
    """Gate期货数据格式解析调试器"""
    
    def __init__(self):
        self.ws_url = "wss://fx-ws.gateio.ws/v4/ws/usdt"
        self.test_symbol = "ADA_USDT"  # 测试交易对
        self.raw_messages = []
        self.parsed_data = []
        self.parsing_errors = []
        
    async def test_live_websocket_parsing(self):
        """实时测试WebSocket数据解析"""
        print("🚀 开始Gate期货WebSocket数据格式解析测试")
        print(f"📡 连接: {self.ws_url}")
        print(f"📊 测试交易对: {self.test_symbol}")
        print("=" * 60)
        
        try:
            async with websockets.connect(self.ws_url) as ws:
                # 订阅期货深度数据
                subscribe_msg = {
                    "time": int(time.time()),
                    "channel": "futures.order_book",
                    "event": "subscribe", 
                    "payload": [self.test_symbol]
                }
                
                await ws.send(json.dumps(subscribe_msg))
                print(f"📤 发送订阅: {json.dumps(subscribe_msg)}")
                
                # 收集数据进行分析
                message_count = 0
                start_time = time.time()
                
                while message_count < 20 and (time.time() - start_time) < 30:
                    try:
                        message = await asyncio.wait_for(ws.recv(), timeout=5.0)
                        message_count += 1
                        
                        print(f"\n📨 消息 #{message_count}:")
                        print(f"原始消息: {message[:200]}...")
                        
                        # 解析消息
                        try:
                            data = json.loads(message)
                            self.raw_messages.append(data)
                            
                            # 分析消息结构
                            await self._analyze_message_structure(data, message_count)
                            
                            # 测试数据解析逻辑
                            if self._is_orderbook_message(data):
                                parsed_result = await self._test_parsing_logic(data)
                                self.parsed_data.append(parsed_result)
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                            
                    except asyncio.TimeoutError:
                        print("⏰ 接收超时，继续等待...")
                        
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            
        # 生成分析报告
        await self._generate_debug_report()
    
    async def _analyze_message_structure(self, data: Dict, msg_num: int):
        """分析消息结构"""
        print(f"🔍 消息结构分析:")
        
        if isinstance(data, dict):
            print(f"   类型: {type(data).__name__}")
            print(f"   顶级字段: {list(data.keys())}")
            
            # 检查关键字段
            if "channel" in data:
                print(f"   频道: {data['channel']}")
            if "event" in data:
                print(f"   事件: {data['event']}")
            if "result" in data:
                print(f"   结果: {type(data['result']).__name__}")
                result = data["result"]
                if isinstance(result, dict):
                    print(f"   结果字段: {list(result.keys())}")
                    
                    # 🔥 关键检查：asks/bids数据结构
                    if "asks" in result:
                        asks = result["asks"]
                        print(f"   📊 asks类型: {type(asks).__name__}, 长度: {len(asks) if hasattr(asks, '__len__') else 'N/A'}")
                        if asks and len(asks) > 0:
                            print(f"   📊 asks样例: {asks[0] if isinstance(asks, list) else asks}")
                        else:
                            print(f"   ⚠️ asks为空或无效!")
                    
                    if "bids" in result:
                        bids = result["bids"]
                        print(f"   📊 bids类型: {type(bids).__name__}, 长度: {len(bids) if hasattr(bids, '__len__') else 'N/A'}")
                        if bids and len(bids) > 0:
                            print(f"   📊 bids样例: {bids[0] if isinstance(bids, list) else bids}")
                        else:
                            print(f"   ⚠️ bids为空或无效!")
                            
                    if "s" in result:
                        print(f"   交易对: {result['s']}")
    
    def _is_orderbook_message(self, data: Dict) -> bool:
        """判断是否为订单簿消息"""
        return (isinstance(data, dict) and 
                data.get("channel") == "futures.order_book" and
                "result" in data)
    
    async def _test_parsing_logic(self, data: Dict) -> Dict:
        """测试现有解析逻辑"""
        print(f"🧪 测试数据解析逻辑:")
        
        result = {
            "message_valid": False,
            "asks_count": 0,
            "bids_count": 0,
            "parsing_errors": [],
            "extracted_data": None
        }
        
        try:
            if not isinstance(data, dict):
                result["parsing_errors"].append("消息不是字典类型")
                return result
                
            if "result" not in data:
                result["parsing_errors"].append("缺少result字段")
                return result
                
            order_data = data["result"]
            if not isinstance(order_data, dict):
                result["parsing_errors"].append("result不是字典类型")
                return result
            
            result["message_valid"] = True
            
            # 🔥 关键测试：asks/bids提取逻辑
            asks = order_data.get("asks", [])
            bids = order_data.get("bids", [])
            
            print(f"   📊 原始asks: {type(asks).__name__} = {asks}")
            print(f"   📊 原始bids: {type(bids).__name__} = {bids}")
            
            # 测试不同的数据格式处理
            processed_asks = self._process_price_level_data(asks, "asks")
            processed_bids = self._process_price_level_data(bids, "bids")
            
            result["asks_count"] = len(processed_asks)
            result["bids_count"] = len(processed_bids)
            
            print(f"   ✅ 处理后asks: {len(processed_asks)}个")
            print(f"   ✅ 处理后bids: {len(processed_bids)}个")
            
            if len(processed_asks) == 0 and len(processed_bids) == 0:
                result["parsing_errors"].append("处理后asks和bids都为空")
            
            result["extracted_data"] = {
                "asks": processed_asks[:3],  # 只保存前3个用于调试
                "bids": processed_bids[:3],
                "symbol": order_data.get("s", "UNKNOWN")
            }
            
        except Exception as e:
            result["parsing_errors"].append(f"解析异常: {str(e)}")
            print(f"   ❌ 解析异常: {e}")
            
        return result
    
    def _process_price_level_data(self, data, data_type: str) -> List[List[float]]:
        """处理价格档位数据 - 测试多种格式"""
        print(f"   🔧 处理{data_type}数据:")
        
        if not data:
            print(f"      ⚠️ {data_type}数据为空")
            return []
            
        processed = []
        
        try:
            if isinstance(data, list):
                print(f"      📋 {data_type}是列表，长度: {len(data)}")
                
                for i, item in enumerate(data[:5]):  # 只处理前5个
                    print(f"      [{i}] 原始: {item} (类型: {type(item).__name__})")
                    
                    if isinstance(item, list) and len(item) >= 2:
                        # 标准格式 [price, size]
                        try:
                            price = float(item[0])
                            size = float(item[1])
                            processed.append([price, size])
                            print(f"      [{i}] ✅ 标准格式: 价格={price}, 数量={size}")
                        except (ValueError, IndexError) as e:
                            print(f"      [{i}] ❌ 标准格式解析失败: {e}")
                            
                    elif isinstance(item, dict):
                        # 字典格式 {"price": "...", "size": "..."}
                        try:
                            price = float(item.get("price", item.get("p", 0)))
                            size = float(item.get("size", item.get("s", item.get("amount", 0))))
                            processed.append([price, size])
                            print(f"      [{i}] ✅ 字典格式: 价格={price}, 数量={size}")
                        except (ValueError, KeyError) as e:
                            print(f"      [{i}] ❌ 字典格式解析失败: {e}")
                            
                    elif isinstance(item, str):
                        # 字符串格式，可能需要进一步解析
                        print(f"      [{i}] ⚠️ 字符串格式需要解析: {item}")
                        
                    else:
                        print(f"      [{i}] ❌ 未知格式: {type(item).__name__}")
                        
            elif isinstance(data, dict):
                print(f"      📚 {data_type}是字典: {list(data.keys())}")
                
            else:
                print(f"      ❌ {data_type}数据类型未知: {type(data).__name__}")
                
        except Exception as e:
            print(f"      ❌ 处理{data_type}数据异常: {e}")
            
        print(f"      🎯 {data_type}最终处理结果: {len(processed)}个有效档位")
        return processed
    
    async def _generate_debug_report(self):
        """生成调试报告"""
        print("\n" + "="*60)
        print("📋 Gate期货数据解析调试报告")
        print("="*60)
        
        print(f"📊 统计信息:")
        print(f"   收到消息总数: {len(self.raw_messages)}")
        print(f"   订单簿消息数: {len(self.parsed_data)}")
        print(f"   解析错误数: {len(self.parsing_errors)}")
        
        # 分析解析结果
        successful_parses = 0
        empty_data_count = 0
        
        for i, result in enumerate(self.parsed_data):
            print(f"\n📨 消息 #{i+1} 解析结果:")
            print(f"   有效消息: {result['message_valid']}")
            print(f"   asks数量: {result['asks_count']}")
            print(f"   bids数量: {result['bids_count']}")
            
            if result['message_valid']:
                successful_parses += 1
                
            if result['asks_count'] == 0 and result['bids_count'] == 0:
                empty_data_count += 1
                print(f"   ⚠️ 空数据问题!")
                
            if result['parsing_errors']:
                print(f"   ❌ 错误: {result['parsing_errors']}")
                
            if result['extracted_data']:
                data = result['extracted_data']
                print(f"   📊 样例数据:")
                print(f"      交易对: {data['symbol']}")
                if data['asks']:
                    print(f"      asks样例: {data['asks'][0]}")
                if data['bids']:
                    print(f"      bids样例: {data['bids'][0]}")
        
        print(f"\n🎯 关键发现:")
        print(f"   成功解析: {successful_parses}/{len(self.parsed_data)}")
        print(f"   空数据问题: {empty_data_count}/{len(self.parsed_data)}")
        
        if empty_data_count > 0:
            print(f"   🚨 确认问题: Gate期货数据格式解析错误导致asks/bids为0!")
            
        # 保存原始数据用于进一步分析
        debug_file = f"/tmp/gate_futures_debug_{int(time.time())}.json"
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump({
                "raw_messages": self.raw_messages,
                "parsed_results": self.parsed_data,
                "summary": {
                    "total_messages": len(self.raw_messages),
                    "successful_parses": successful_parses,
                    "empty_data_count": empty_data_count
                }
            }, f, ensure_ascii=False, indent=2)
            
        print(f"💾 调试数据已保存: {debug_file}")

async def main():
    """主函数"""
    print("🔍 Gate期货数据格式解析错误动态测试")
    print("问题描述: Gate快照数据不完整 asks=0 bids=0")
    print("测试目标: 找出数据格式解析错误的根本原因")
    
    debugger = GateFuturesParsingDebugger()
    await debugger.test_live_websocket_parsing()

if __name__ == "__main__":
    asyncio.run(main())