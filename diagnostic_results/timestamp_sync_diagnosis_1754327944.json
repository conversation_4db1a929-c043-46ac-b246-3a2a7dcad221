{"diagnosis_time": "2025-08-04 19:19:04", "problem_description": "Gate.io WebSocket timestamp freshness check failed", "results": {"system_time": {"current_timestamp_ms": 1754327944081, "problem_timestamp": 1754326029171, "time_diff_ms": 1914910, "time_diff_seconds": 1914.91}, "gate_api_sync": {"success": true, "server_timestamp": 1754327944112, "network_latency_ms": 33.450927734375, "time_offset_ms": -3.351318359375, "offset_status": "normal"}, "websocket_simulation": {"messages_tested": 3, "extraction_successful": true, "max_age_threshold_ms": 1000}}, "conclusions": ["WebSocket data stream delay/blocking is the main cause", "Timestamp extraction logic is correct", "Need to optimize WebSocket connection and monitoring"]}