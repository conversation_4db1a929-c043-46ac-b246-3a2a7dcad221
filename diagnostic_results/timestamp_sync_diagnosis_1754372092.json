{"diagnosis_time": "2025-08-05 07:34:52", "problem_description": "Gate.io WebSocket timestamp freshness check failed", "results": {"system_time": {"current_timestamp_ms": 1754372092181, "problem_timestamp": 1754326029171, "time_diff_ms": 46063010, "time_diff_seconds": 46063.01}, "gate_api_sync": {"success": true, "server_timestamp": 1754372092198, "network_latency_ms": 18.8271484375, "time_offset_ms": -2.667724609375, "offset_status": "normal"}, "websocket_simulation": {"messages_tested": 3, "extraction_successful": true, "max_age_threshold_ms": 1000}}, "conclusions": ["WebSocket data stream delay/blocking is the main cause", "Timestamp extraction logic is correct", "Need to optimize WebSocket connection and monitoring"]}