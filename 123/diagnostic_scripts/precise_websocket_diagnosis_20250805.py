#!/usr/bin/env python3
"""
Gate.io和OKX数据阻塞精确诊断脚本 - 2025-08-05专版
基于最新日志分析，针对发现的核心问题进行系统诊断

核心发现问题：
1. 时间戳同步严重失败 - Gate和OKX差异高达5097ms  
2. 数据大量被丢弃 - OKX数据时间戳老化38秒
3. 套利机会全部丢失 - 时间戳不同步导致无有效检测
4. OKX API限流 - 50011错误频繁出现
5. Bybit SHIB-USDT交易对问题导致订阅失败

执行: python diagnostic_scripts/precise_websocket_diagnosis_20250805.py
"""

import re
import json
import asyncio
import aiohttp
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
import sys
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseWebSocketDiagnosis20250805:
    def __init__(self):
        self.base_path = Path("/root/myproject/123/70 gate和okx还是数据阻塞/123")
        self.logs_path = self.base_path / "logs"
        self.results = {
            "diagnosis_timestamp": int(time.time()),
            "diagnosis_time": datetime.now().isoformat(),
            "log_date": "2025-08-05",
            "critical_findings": [],
            "performance_analysis": {},
            "api_issues": {},
            "data_flow_problems": {},
            "fix_priorities": [],
            "validation_checklist": []
        }
    
    def analyze_timestamp_sync_crisis(self):
        """分析时间戳同步危机 - 这是数据阻塞的核心原因"""
        logger.info("🔴 分析时间戳同步危机...")
        
        perf_log = self.logs_path / "websocket_performance_20250805.log"
        if not perf_log.exists():
            logger.warning(f"性能日志文件不存在: {perf_log}")
            return
        
        # 分析最新的时间戳差异
        sync_failures = []
        freshness_failures = []
        lost_opportunities = []
        
        try:
            # 读取最后1000行来分析最新状况
            with open(perf_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-1000:] if len(lines) > 1000 else lines
                recent_content = ''.join(recent_lines)
            
            # 1. 时间戳不同步问题
            sync_pattern = r'价格数据时间戳不同步.*?time_diff_ms.*?(\d+).*?max_diff_ms.*?(\d+).*?combo_name.*?([a-z_]+)'
            for match in re.finditer(sync_pattern, recent_content):
                time_diff = int(match.group(1))
                max_diff = int(match.group(2))
                combo = match.group(3)
                
                sync_failures.append({
                    "time_diff_ms": time_diff,
                    "max_allowed_ms": max_diff,
                    "combo_name": combo,
                    "severity": "critical" if time_diff > 3000 else "high"
                })
            
            # 2. 数据新鲜度失败
            freshness_pattern = r'数据新鲜度检查失败.*?exchange.*?(\w+).*?timestamp_age_ms.*?(\d+).*?max_age_ms.*?(\d+)'
            for match in re.finditer(freshness_pattern, recent_content):
                exchange = match.group(1)
                age_ms = int(match.group(2))
                max_age = int(match.group(3))
                
                freshness_failures.append({
                    "exchange": exchange,
                    "age_ms": age_ms,
                    "max_allowed_ms": max_age,
                    "severity": "critical" if age_ms > 10000 else "high"
                })
        
        except Exception as e:
            logger.error(f"分析时间戳同步问题时出错: {e}")
        
        # 统计分析
        if sync_failures:
            by_combo = {}
            for failure in sync_failures:
                combo = failure["combo_name"]
                if combo not in by_combo:
                    by_combo[combo] = []
                by_combo[combo].append(failure["time_diff_ms"])
            
            # 计算统计数据
            all_diffs = [f["time_diff_ms"] for f in sync_failures]
            avg_diff = sum(all_diffs) / len(all_diffs)
            max_diff = max(all_diffs)
            critical_count = len([f for f in sync_failures if f["severity"] == "critical"])
            
            self.results["critical_findings"].append({
                "issue": "TIMESTAMP_SYNC_CRISIS",
                "severity": "CRITICAL",
                "description": f"时间戳同步严重失败，最大差异{max_diff}ms，平均差异{avg_diff:.0f}ms",
                "impact": "所有跨交易所套利机会被丢弃，系统无法正常工作",
                "stats": {
                    "total_failures": len(sync_failures),
                    "critical_failures": critical_count,
                    "max_diff_ms": max_diff,
                    "avg_diff_ms": round(avg_diff, 2),
                    "affected_combos": list(by_combo.keys())
                }
            })
        
        if freshness_failures:
            by_exchange = {}
            for failure in freshness_failures:
                exchange = failure["exchange"]
                if exchange not in by_exchange:
                    by_exchange[exchange] = []
                by_exchange[exchange].append(failure["age_ms"])
            
            self.results["data_flow_problems"]["freshness_failures"] = {
                "total_failures": len(freshness_failures),
                "by_exchange": {ex: {"count": len(ages), "avg_age_ms": sum(ages)/len(ages), "max_age_ms": max(ages)} 
                               for ex, ages in by_exchange.items()},
                "critical_count": len([f for f in freshness_failures if f["severity"] == "critical"])
            }
    
    def analyze_okx_api_rate_limiting(self):
        """分析OKX API限流问题"""
        logger.info("🔴 分析OKX API限流问题...")
        
        error_log = self.logs_path / "error_20250805.log"
        if not error_log.exists():
            logger.warning(f"错误日志文件不存在: {error_log}")
            return
        
        try:
            with open(error_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取所有50011错误
            rate_limit_errors = []
            error_blocks = re.findall(r'(2025-08-05 \d+:\d+:\d+\.\d+.*?错误代码: 50011.*?请求方法: \w+)', content, re.DOTALL)
            
            for block in error_blocks:
                # 提取时间戳
                time_match = re.search(r'2025-08-05 (\d+:\d+:\d+)\.\d+', block)
                # 提取端点
                endpoint_match = re.search(r'请求端点: ([^\n]+)', block)
                # 提取方法
                method_match = re.search(r'请求方法: (\w+)', block)
                
                if time_match and endpoint_match and method_match:
                    rate_limit_errors.append({
                        "time": time_match.group(1),
                        "endpoint": endpoint_match.group(1).strip(),
                        "method": method_match.group(1)
                    })
            
            if rate_limit_errors:
                # 统计分析
                endpoint_counts = {}
                time_distribution = {}
                
                for error in rate_limit_errors:
                    endpoint = error["endpoint"]
                    hour = error["time"][:2]
                    
                    endpoint_counts[endpoint] = endpoint_counts.get(endpoint, 0) + 1
                    time_distribution[hour] = time_distribution.get(hour, 0) + 1
                
                self.results["api_issues"]["okx_rate_limiting"] = {
                    "total_errors": len(rate_limit_errors),
                    "affected_endpoints": list(endpoint_counts.keys()),
                    "endpoint_error_counts": endpoint_counts,
                    "time_distribution": time_distribution,
                    "most_problematic": max(endpoint_counts.items(), key=lambda x: x[1]) if endpoint_counts else None
                }
                
                # 添加到关键发现
                most_affected = max(endpoint_counts.items(), key=lambda x: x[1])
                self.results["critical_findings"].append({
                    "issue": "OKX_API_RATE_LIMITING",
                    "severity": "HIGH",
                    "description": f"OKX API限流错误{len(rate_limit_errors)}次，主要影响{most_affected[0]}",
                    "impact": "API调用失败，影响交易所初始化和数据获取",
                    "immediate_action": "降低API调用频率，实现智能退避机制"
                })
        
        except Exception as e:
            logger.error(f"分析OKX API限流问题时出错: {e}")
    
    def analyze_bybit_trading_pair_issues(self):
        """分析Bybit交易对问题"""
        logger.info("🟡 分析Bybit交易对问题...")
        
        error_log = self.logs_path / "error_20250805.log"
        if not error_log.exists():
            return
        
        try:
            with open(error_log, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找SHIB相关错误
            shib_errors = re.findall(r'Bybit.*?SHIB-USDT.*?10001: params error: symbol invalid', content)
            subscription_failures = re.findall(r'\[BYBIT\] orderbook订阅失败', content)
            
            if shib_errors or subscription_failures:
                self.results["api_issues"]["bybit_symbol_issues"] = {
                    "shib_symbol_errors": len(shib_errors),
                    "subscription_failures": len(subscription_failures),
                    "problem_symbol": "SHIB-USDT",
                    "root_cause": "交易对格式不匹配或交易对不存在"
                }
                
                if len(shib_errors) > 5:  # 频繁错误
                    self.results["critical_findings"].append({
                        "issue": "BYBIT_SYMBOL_VALIDATION",
                        "severity": "MEDIUM",
                        "description": f"Bybit SHIB-USDT交易对验证失败{len(shib_errors)}次",
                        "impact": "Bybit期货数据缺失，影响期现套利策略",
                        "immediate_action": "验证交易对格式，添加交易对有效性检查"
                    })
        
        except Exception as e:
            logger.error(f"分析Bybit交易对问题时出错: {e}")
    
    def analyze_data_flow_architecture(self):
        """分析数据流架构问题"""
        logger.info("🔍 分析数据流架构问题...")
        
        # 检查关键WebSocket文件
        ws_files = {
            "gate_ws": self.base_path / "websocket" / "gate_ws.py",
            "okx_ws": self.base_path / "websocket" / "okx_ws.py", 
            "bybit_ws": self.base_path / "websocket" / "bybit_ws.py",
            "unified_formatter": self.base_path / "websocket" / "unified_data_formatter.py"
        }
        
        architecture_issues = []
        
        for name, file_path in ws_files.items():
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查时间戳处理
                    if "timestamp" in content and "time.time()" in content:
                        if "server_time" not in content and "sync" not in content:
                            architecture_issues.append({
                                "file": name,
                                "issue": "LOCAL_TIMESTAMP_USAGE",
                                "description": "使用本地时间戳而非服务器时间戳"
                            })
                    
                    # 检查数据新鲜度处理
                    if "max_age" in content or "timestamp_age" in content:
                        if "1000" in content:  # 1秒阈值可能太严格
                            architecture_issues.append({
                                "file": name,
                                "issue": "STRICT_FRESHNESS_THRESHOLD",
                                "description": "数据新鲜度阈值可能过于严格(1000ms)"
                            })
                
                except Exception as e:
                    logger.error(f"检查{name}时出错: {e}")
        
        if architecture_issues:
            self.results["data_flow_problems"]["architecture_issues"] = architecture_issues
    
    def generate_fix_priorities(self):
        """生成修复优先级"""
        logger.info("🎯 生成修复优先级...")
        
        priorities = []
        
        # 1. 时间戳同步危机 - 最高优先级
        if any(f["issue"] == "TIMESTAMP_SYNC_CRISIS" for f in self.results["critical_findings"]):
            priorities.append({
                "priority": 1,
                "urgency": "CRITICAL",
                "task": "修复时间戳同步机制",
                "description": "系统核心问题，所有套利机会被丢弃",
                "estimated_impact": "修复后恢复90%以上的数据流",
                "files_to_modify": [
                    "websocket/unified_data_formatter.py",
                    "websocket/gate_ws.py", 
                    "websocket/okx_ws.py",
                    "core/unified_order_spread_calculator.py"
                ],
                "actions": [
                    "实现服务器时间戳同步机制",
                    "调整数据新鲜度阈值从1000ms到3000ms",
                    "添加时间戳校正算法",
                    "优化跨交易所时间戳对齐"
                ]
            })
        
        # 2. OKX API限流 - 高优先级
        if "okx_rate_limiting" in self.results["api_issues"]:
            priorities.append({
                "priority": 2,
                "urgency": "HIGH", 
                "task": "解决OKX API限流问题",
                "description": "频繁的API限流影响系统初始化",
                "estimated_impact": "减少90%的API错误",
                "files_to_modify": [
                    "exchanges/okx_exchange.py",
                    "config/settings.py"
                ],
                "actions": [
                    "降低API调用频率",
                    "实现指数退避重试机制",
                    "优化批量请求策略",
                    "添加请求队列管理"
                ]
            })
        
        # 3. Bybit交易对问题 - 中优先级
        if "bybit_symbol_issues" in self.results["api_issues"]:
            priorities.append({
                "priority": 3,
                "urgency": "MEDIUM",
                "task": "修复Bybit交易对验证",
                "description": "SHIB-USDT等交易对格式问题",
                "estimated_impact": "恢复affected交易对的期货数据",
                "files_to_modify": [
                    "exchanges/bybit_exchange.py",
                    "utils/currency_adapter.py"
                ],
                "actions": [
                    "添加交易对有效性预检查",
                    "修复交易对格式转换",
                    "实现动态交易对过滤",
                    "优化错误处理机制"
                ]
            })
        
        self.results["fix_priorities"] = priorities
    
    def create_validation_checklist(self):
        """创建验证清单"""
        logger.info("📋 创建验证清单...")
        
        checklist = [
            {
                "category": "时间戳同步验证",
                "checks": [
                    "验证Gate.io和OKX时间戳差异 < 2000ms",
                    "确认数据新鲜度检查通过率 > 80%",
                    "检查套利机会检测恢复正常",
                    "验证跨交易所价格数据同步"
                ]
            },
            {
                "category": "API调用验证", 
                "checks": [
                    "确认OKX API错误率 < 5%",
                    "验证API调用频率在限制范围内",
                    "检查重试机制正常工作",
                    "确认所有交易所初始化成功"
                ]
            },
            {
                "category": "WebSocket连接验证",
                "checks": [
                    "确认所有WebSocket连接稳定",
                    "验证订阅失败率 < 2%",
                    "检查数据流连续性",
                    "确认错误恢复机制正常"
                ]
            },
            {
                "category": "套利功能验证",
                "checks": [
                    "验证期现价差计算正确",
                    "确认套利机会识别恢复",
                    "检查风险管理功能正常",
                    "验证执行引擎响应及时"
                ]
            }
        ]
        
        self.results["validation_checklist"] = checklist
    
    async def check_current_system_status(self):
        """检查当前系统状态"""
        logger.info("🔍 检查当前系统状态...")
        
        # 检查最新日志时间戳
        current_status = {
            "log_freshness": {},
            "api_connectivity": {},
            "error_rates": {}
        }
        
        # 检查日志新鲜度
        log_files = [
            "error_20250805.log",
            "websocket_performance_20250805.log", 
            "gate_exchange.log"
        ]
        
        for log_file in log_files:
            log_path = self.logs_path / log_file
            if log_path.exists():
                stat = log_path.stat()
                last_modified = datetime.fromtimestamp(stat.st_mtime)
                age_minutes = (datetime.now() - last_modified).total_seconds() / 60
                current_status["log_freshness"][log_file] = {
                    "last_modified": last_modified.isoformat(),
                    "age_minutes": round(age_minutes, 2),
                    "status": "fresh" if age_minutes < 10 else "stale"
                }
        
        # 简单的API连通性检查
        api_endpoints = {
            "gate": "https://api.gateio.ws/api/v4/spot/time",
            "okx": "https://www.okx.com/api/v5/public/time", 
            "bybit": "https://api.bybit.com/v5/market/time"
        }
        
        async with aiohttp.ClientSession() as session:
            for exchange, url in api_endpoints.items():
                try:
                    start_time = time.time()
                    async with session.get(url, timeout=5) as resp:
                        response_time = (time.time() - start_time) * 1000
                        current_status["api_connectivity"][exchange] = {
                            "status": "ok" if resp.status == 200 else "error",
                            "http_code": resp.status,
                            "response_time_ms": round(response_time, 2)
                        }
                except Exception as e:
                    current_status["api_connectivity"][exchange] = {
                        "status": "error",
                        "error": str(e)
                    }
        
        self.results["current_system_status"] = current_status
    
    def save_diagnosis_results(self):
        """保存诊断结果"""
        results_file = self.base_path / "diagnostic_results" / f"precise_websocket_diagnosis_{self.results['diagnosis_timestamp']}.json"
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"诊断结果已保存到: {results_file}")
        return results_file
    
    def print_executive_summary(self):
        """打印执行摘要"""
        print("\n" + "="*80)
        print("🔍 Gate.io和OKX数据阻塞精确诊断报告 - 2025-08-05")
        print("="*80)
        
        # 关键发现
        if self.results["critical_findings"]:
            print(f"\n🚨 关键发现 ({len(self.results['critical_findings'])}个):")
            for finding in self.results["critical_findings"]:
                severity_icon = "🔴" if finding["severity"] == "CRITICAL" else "🟡" if finding["severity"] == "HIGH" else "🟢"
                print(f"  {severity_icon} [{finding['severity']}] {finding['issue']}")
                print(f"     {finding['description']}")
                print(f"     影响: {finding['impact']}")
                if "immediate_action" in finding:
                    print(f"     立即行动: {finding['immediate_action']}")
                print()
        
        # 修复优先级
        if self.results["fix_priorities"]:
            print(f"🎯 修复优先级 ({len(self.results['fix_priorities'])}项):")
            for priority in self.results["fix_priorities"]:
                urgency_icon = "🔴" if priority["urgency"] == "CRITICAL" else "🟡" if priority["urgency"] == "HIGH" else "🟢"
                print(f"  {urgency_icon} 优先级{priority['priority']}: {priority['task']}")
                print(f"     预期影响: {priority['estimated_impact']}")
                print(f"     文件: {', '.join(priority['files_to_modify'][:2])}{'...' if len(priority['files_to_modify']) > 2 else ''}")
                print()
        
        # 系统状态
        if "current_system_status" in self.results:
            status = self.results["current_system_status"]
            print("📊 当前系统状态:")
            
            if "api_connectivity" in status:
                print("  API连通性:")
                for exchange, conn in status["api_connectivity"].items():
                    status_icon = "✅" if conn["status"] == "ok" else "❌"
                    print(f"    {status_icon} {exchange}: {conn['status']}")
            
            if "log_freshness" in status:
                fresh_logs = sum(1 for log in status["log_freshness"].values() if log["status"] == "fresh")
                total_logs = len(status["log_freshness"])
                print(f"  日志新鲜度: {fresh_logs}/{total_logs} 新鲜")
        
        print("\n" + "="*80)
        print("📋 建议立即执行第一优先级任务以恢复系统正常运行")
        print("="*80)
    
    async def run_diagnosis(self):
        """运行完整诊断"""
        logger.info("🚀 开始Gate.io和OKX数据阻塞精确诊断...")
        
        # 执行各项分析
        self.analyze_timestamp_sync_crisis()
        self.analyze_okx_api_rate_limiting()
        self.analyze_bybit_trading_pair_issues()
        self.analyze_data_flow_architecture()
        
        # 检查当前状态
        await self.check_current_system_status()
        
        # 生成修复计划
        self.generate_fix_priorities()
        self.create_validation_checklist()
        
        # 保存和输出结果
        results_file = self.save_diagnosis_results()
        self.print_executive_summary()
        
        return self.results, results_file

async def main():
    """主函数"""
    diagnosis = PreciseWebSocketDiagnosis20250805()
    results, results_file = await diagnosis.run_diagnosis()
    
    # 根据发现的问题返回适当的退出码
    critical_count = len([f for f in results["critical_findings"] if f["severity"] == "CRITICAL"])
    high_count = len([f for f in results["critical_findings"] if f["severity"] == "HIGH"])
    
    if critical_count > 0:
        logger.info(f"发现{critical_count}个严重问题，需要立即处理")
        sys.exit(1)
    elif high_count > 0:
        logger.info(f"发现{high_count}个高优先级问题")
        sys.exit(2)
    else:
        logger.info("系统状态良好")
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())