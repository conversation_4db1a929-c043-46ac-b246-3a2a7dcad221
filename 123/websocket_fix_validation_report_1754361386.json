{"basic_core_tests": {"test_unified_modules_usage": {"passed": true, "details": "All unified modules correctly integrated"}, "test_gate_websocket_initialization": {"passed": true, "details": "Gate.io WebSocket initialization correct"}, "test_gate_orderbook_processing": {"passed": true, "details": "Gate.io orderbook processing mechanism correct", "processed_data": {"data_type": "orderbook", "symbol": "BTC-USDT", "exchange": "gate", "market_type": "spot", "asks": [[50000.1, 0.1], [50000.2, 0.2], [50000.3, 0.3]], "bids": [[49999.9, 0.1], [49999.8, 0.2], [49999.7, 0.3]], "timestamp": 1754361385782, "asks_depth": 3, "bids_depth": 3, "price": 49999.9, "best_bid": 49999.9, "best_ask": 50000.1, "spread": 0.19999999999708962, "spread_percent": 0.0004000007999957792, "spread_bps": 0.04000007999957792, "incomplete_orderbook": false, "low_liquidity": true, "data_quality_score": 70}}, "test_error_handling_robustness": {"passed": true, "details": "Error handling robust: 5/5 cases handled gracefully"}, "test_parameter_validation": {"passed": true, "details": "Parameter validation working correctly"}}, "system_integration_tests": {"test_multi_exchange_consistency": {"passed": true, "details": "Multi-exchange consistency verified"}, "test_data_flow_integration": {"passed": true, "details": "Data flow integration working correctly"}, "test_timestamp_synchronization": {"passed": true, "details": "Timestamp synchronization working, diff: 0ms"}}, "production_environment_tests": {"test_performance_benchmarks": {"passed": true, "details": "Performance benchmark passed: 0.27ms average processing time"}, "test_concurrent_processing": {"passed": true, "details": "Concurrent processing: 50/50 successful in 8.16ms"}, "test_memory_usage": {"passed": true, "details": "Memory usage acceptable: 0.12MB increase"}}, "start_time": "2025-08-05T04:36:25.320295", "total_passed": 0, "total_failed": 0, "critical_errors": [], "end_time": "2025-08-05T04:36:26.106352", "total_tests": 11, "success_rate": 100.0}