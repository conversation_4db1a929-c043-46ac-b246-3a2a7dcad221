#!/usr/bin/env python3
"""
Gate.io时间戳问题专项诊断
分析Gate.io数据延迟的根本原因
"""

import asyncio
import json
import time
import logging
from datetime import datetime
import websockets

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

class GateTimestampDiagnostic:
    """Gate.io时间戳诊断器"""
    
    def __init__(self):
        self.spot_url = "wss://api.gateio.ws/ws/v4/"
        self.futures_url = "wss://fx-ws.gateio.ws/v4/ws/usdt"
        self.results = []
        
    async def test_spot_timestamps(self):
        """测试现货时间戳"""
        logger.info("🔍 开始测试Gate.io现货时间戳...")
        
        try:
            async with websockets.connect(self.spot_url) as ws:
                # 订阅BTC_USDT现货订单簿
                subscribe_msg = {
                    "time": int(time.time()),
                    "channel": "spot.order_book",
                    "event": "subscribe",
                    "payload": ["BTC_USDT", "50", "100ms"]
                }
                
                await ws.send(json.dumps(subscribe_msg))
                logger.info(f"📤 发送现货订阅: {subscribe_msg}")
                
                # 接收多个消息进行分析
                for i in range(5):
                    try:
                        message = await asyncio.wait_for(ws.recv(), timeout=10)
                        data = json.loads(message)
                        
                        receive_time = int(time.time() * 1000)
                        
                        # 分析时间戳
                        await self._analyze_message_timestamp(data, receive_time, "spot", i+1)
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ 现货消息{i+1}接收超时")
                        break
                    except Exception as e:
                        logger.error(f"❌ 现货消息{i+1}处理异常: {e}")
                        
        except Exception as e:
            logger.error(f"❌ 现货连接异常: {e}")
    
    async def test_futures_timestamps(self):
        """测试期货时间戳"""
        logger.info("🔍 开始测试Gate.io期货时间戳...")
        
        try:
            async with websockets.connect(self.futures_url) as ws:
                # 订阅BTC_USDT期货订单簿
                subscribe_msg = {
                    "time": int(time.time()),
                    "channel": "futures.order_book",
                    "event": "subscribe",
                    "payload": ["BTC_USDT"]
                }
                
                await ws.send(json.dumps(subscribe_msg))
                logger.info(f"📤 发送期货订阅: {subscribe_msg}")
                
                # 接收多个消息进行分析
                for i in range(5):
                    try:
                        message = await asyncio.wait_for(ws.recv(), timeout=10)
                        data = json.loads(message)
                        
                        receive_time = int(time.time() * 1000)
                        
                        # 分析时间戳
                        await self._analyze_message_timestamp(data, receive_time, "futures", i+1)
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ 期货消息{i+1}接收超时")
                        break
                    except Exception as e:
                        logger.error(f"❌ 期货消息{i+1}处理异常: {e}")
                        
        except Exception as e:
            logger.error(f"❌ 期货连接异常: {e}")
    
    async def _analyze_message_timestamp(self, data, receive_time, market_type, msg_num):
        """分析消息时间戳"""
        try:
            channel = data.get("channel", "")
            event = data.get("event", "")
            
            logger.info(f"📨 {market_type}消息{msg_num}: channel={channel}, event={event}")
            
            # 查找时间戳字段
            timestamps_found = {}
            
            # 检查消息级别的时间戳
            if "time" in data:
                timestamps_found["msg_time"] = data["time"] * 1000  # 转换为毫秒
            if "time_ms" in data:
                timestamps_found["msg_time_ms"] = data["time_ms"]
            
            # 检查result中的时间戳
            result = data.get("result", {})
            if isinstance(result, dict):
                if "t" in result:
                    timestamps_found["result_t"] = result["t"]
                if "time" in result:
                    timestamps_found["result_time"] = result["time"] * 1000
                if "timestamp" in result:
                    timestamps_found["result_timestamp"] = result["timestamp"]
            elif isinstance(result, list) and result:
                # 如果result是列表，检查第一个元素
                first_item = result[0] if result else {}
                if isinstance(first_item, dict):
                    if "t" in first_item:
                        timestamps_found["result_list_t"] = first_item["t"]
                    if "time" in first_item:
                        timestamps_found["result_list_time"] = first_item["time"] * 1000
            
            # 分析每个时间戳
            analysis = {
                "message_num": msg_num,
                "market_type": market_type,
                "channel": channel,
                "event": event,
                "receive_time": receive_time,
                "timestamps": {},
                "delays": {}
            }
            
            for ts_name, ts_value in timestamps_found.items():
                try:
                    ts_ms = int(ts_value)
                    delay_ms = receive_time - ts_ms
                    
                    analysis["timestamps"][ts_name] = ts_ms
                    analysis["delays"][ts_name] = delay_ms
                    
                    # 转换为可读时间
                    ts_dt = datetime.fromtimestamp(ts_ms / 1000)
                    receive_dt = datetime.fromtimestamp(receive_time / 1000)
                    
                    if delay_ms > 5000:  # 超过5秒
                        logger.error(f"❌ {market_type}消息{msg_num} {ts_name}: 严重延迟 {delay_ms}ms")
                        logger.error(f"   时间戳时间: {ts_dt}")
                        logger.error(f"   接收时间: {receive_dt}")
                    elif delay_ms > 1000:  # 超过1秒
                        logger.warning(f"⚠️ {market_type}消息{msg_num} {ts_name}: 延迟 {delay_ms}ms")
                    else:
                        logger.info(f"✅ {market_type}消息{msg_num} {ts_name}: 正常 {delay_ms}ms")
                        
                except (ValueError, TypeError) as e:
                    logger.error(f"❌ {market_type}消息{msg_num} {ts_name}: 时间戳格式错误 {ts_value} - {e}")
            
            self.results.append(analysis)
            
        except Exception as e:
            logger.error(f"❌ 分析{market_type}消息{msg_num}时异常: {e}")
    
    def print_summary(self):
        """打印分析汇总"""
        logger.info("=" * 80)
        logger.info("📊 Gate.io时间戳分析汇总")
        logger.info("=" * 80)
        
        if not self.results:
            logger.warning("⚠️ 没有收集到任何数据")
            return
        
        # 按市场类型分组
        spot_results = [r for r in self.results if r["market_type"] == "spot"]
        futures_results = [r for r in self.results if r["market_type"] == "futures"]
        
        for market_type, results in [("现货", spot_results), ("期货", futures_results)]:
            if not results:
                continue
                
            logger.info(f"\n📈 {market_type}市场分析:")
            
            # 统计各时间戳字段的延迟
            delay_stats = {}
            for result in results:
                for ts_name, delay in result["delays"].items():
                    if ts_name not in delay_stats:
                        delay_stats[ts_name] = []
                    delay_stats[ts_name].append(delay)
            
            for ts_name, delays in delay_stats.items():
                if delays:
                    avg_delay = sum(delays) / len(delays)
                    max_delay = max(delays)
                    min_delay = min(delays)
                    
                    logger.info(f"  {ts_name}字段:")
                    logger.info(f"    平均延迟: {avg_delay:.1f}ms")
                    logger.info(f"    最大延迟: {max_delay}ms")
                    logger.info(f"    最小延迟: {min_delay}ms")
                    
                    if avg_delay > 5000:
                        logger.error(f"    ❌ 严重延迟问题！")
                    elif avg_delay > 1000:
                        logger.warning(f"    ⚠️ 延迟较高")
                    else:
                        logger.info(f"    ✅ 延迟正常")
        
        # 保存详细结果
        with open('gate_timestamp_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 详细分析结果已保存到: gate_timestamp_analysis.json")

async def main():
    """主函数"""
    diagnostic = GateTimestampDiagnostic()
    
    # 并行测试现货和期货
    await asyncio.gather(
        diagnostic.test_spot_timestamps(),
        diagnostic.test_futures_timestamps()
    )
    
    # 打印汇总
    diagnostic.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
