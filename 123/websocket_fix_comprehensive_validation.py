#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io数据阻塞修复 - 机构级别全面验证测试
确保修复100%无问题，符合三交易所一致性要求
"""

import asyncio
import json
import time
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class WebSocketFixComprehensiveValidation:
    """机构级别WebSocket修复验证"""

    def __init__(self):
        self.test_results = {
            "basic_core_tests": {},
            "system_integration_tests": {},
            "production_environment_tests": {},
            "start_time": datetime.now().isoformat(),
            "total_passed": 0,
            "total_failed": 0,
            "critical_errors": []
        }
        self.total_passed = 0
        self.total_failed = 0

    async def run_all_tests(self):
        """运行所有测试"""
        print("🔥 开始机构级别全面验证测试...")
        
        # 1. 基础核心测试
        await self.test_basic_core_functionality()
        
        # 2. 系统集成测试
        await self.test_system_integration()
        
        # 3. 生产环境测试
        await self.test_production_environment()
        
        # 生成测试报告
        await self.generate_test_report()

    async def test_basic_core_functionality(self):
        """基础核心测试：模块单元功能验证"""
        print("\n📊 === 基础核心测试 ===")
        
        tests = [
            self.test_unified_modules_usage,
            self.test_gate_websocket_initialization,
            self.test_gate_orderbook_processing,
            self.test_error_handling_robustness,
            self.test_parameter_validation
        ]
        
        for test in tests:
            try:
                result = await test()
                test_name = test.__name__
                self.test_results["basic_core_tests"][test_name] = result
                if result.get("passed", False):
                    self.total_passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    self.total_failed += 1
                    print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    if result.get("critical", False):
                        self.test_results["critical_errors"].append(f"{test_name}: {result.get('error', '')}")
            except Exception as e:
                self.total_failed += 1
                error_msg = f"Exception in {test.__name__}: {str(e)}"
                print(f"🚨 {error_msg}")
                self.test_results["critical_errors"].append(error_msg)

    async def test_unified_modules_usage(self):
        """测试统一模块使用情况"""
        try:
            # 检查Gate.io是否正确使用统一模块
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            
            # 验证统一模块导入
            required_attrs = [
                'timestamp_processor',
                'pool_manager', 
                '_log_data_received',
                'orderbook_locks'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if not hasattr(client, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                return {
                    "passed": False,
                    "critical": True,
                    "error": f"Missing unified module attributes: {missing_attrs}"
                }
            
            # 验证时间戳处理器类型
            if not hasattr(client.timestamp_processor, 'get_synced_timestamp'):
                return {
                    "passed": False,
                    "critical": True,
                    "error": "timestamp_processor missing get_synced_timestamp method"
                }
            
            return {
                "passed": True,
                "details": "All unified modules correctly integrated"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "critical": True,
                "error": f"Failed to verify unified modules: {str(e)}"
            }

    async def test_gate_websocket_initialization(self):
        """测试Gate.io WebSocket初始化"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            # 测试现货和期货初始化
            spot_client = GateWebSocketClient("spot")
            futures_client = GateWebSocketClient("futures")
            
            # 验证基本属性
            if spot_client.market_type != "spot":
                return {"passed": False, "error": "Spot client market_type incorrect"}
            
            if futures_client.market_type != "futures":
                return {"passed": False, "error": "Futures client market_type incorrect"}
            
            # 验证URL设置
            spot_url = spot_client.get_ws_url()
            futures_url = futures_client.get_ws_url()
            
            if "api.gateio.ws" not in spot_url:
                return {"passed": False, "error": f"Spot URL incorrect: {spot_url}"}
            
            if "fx-ws.gateio.ws" not in futures_url:
                return {"passed": False, "error": f"Futures URL incorrect: {futures_url}"}
            
            # 验证心跳间隔统一性
            if spot_client.heartbeat_interval != 20:
                return {"passed": False, "error": f"Heartbeat interval not unified: {spot_client.heartbeat_interval}"}
            
            return {
                "passed": True,
                "details": "Gate.io WebSocket initialization correct"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "critical": True,
                "error": f"Initialization test failed: {str(e)}"
            }

    async def test_gate_orderbook_processing(self):
        """测试Gate.io订单簿处理机制"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT"])
            
            # 模拟Gate.io订单簿数据格式
            test_orderbook_data = {
                "currency_pair": "BTC_USDT",
                "asks": [
                    ["50000.1", "0.1"],
                    ["50000.2", "0.2"],
                    ["50000.3", "0.3"]
                ],
                "bids": [
                    ["49999.9", "0.1"],
                    ["49999.8", "0.2"],
                    ["49999.7", "0.3"]
                ],
                "timestamp": int(time.time() * 1000)
            }
            
            # 创建一个捕获输出的回调
            received_data = {}
            
            def capture_market_data(data):
                received_data.update(data)
            
            # 注册回调
            client.register_callback("market_data", capture_market_data)
            
            # 处理订单簿数据
            await client._handle_orderbook(test_orderbook_data)
            
            # 验证处理结果
            if not received_data:
                return {"passed": False, "error": "No market data emitted"}
            
            # 验证数据格式
            required_fields = ["asks", "bids", "symbol", "exchange", "timestamp"]
            missing_fields = [field for field in required_fields if field not in received_data]
            
            if missing_fields:
                return {"passed": False, "error": f"Missing fields in output: {missing_fields}"}
            
            # 验证数据内容
            if received_data["exchange"] != "gate":
                return {"passed": False, "error": f"Exchange field incorrect: {received_data['exchange']}"}
            
            if len(received_data["asks"]) == 0 or len(received_data["bids"]) == 0:
                return {"passed": False, "error": "Empty asks or bids after processing"}
            
            return {
                "passed": True,
                "details": "Gate.io orderbook processing mechanism correct",
                "processed_data": received_data
            }
            
        except Exception as e:
            return {
                "passed": False,
                "critical": True,
                "error": f"Orderbook processing test failed: {str(e)}"
            }

    async def test_error_handling_robustness(self):
        """测试错误处理健壮性"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            
            # 测试各种异常情况
            test_cases = [
                # 空数据
                {},
                # 缺少关键字段
                {"currency_pair": "BTC_USDT"},
                # 错误的数据格式
                {"currency_pair": "BTC_USDT", "asks": "invalid", "bids": []},
                # 价格格式错误
                {"currency_pair": "BTC_USDT", "asks": [["invalid_price", "0.1"]], "bids": [["49999", "0.1"]]},
                # 数量格式错误
                {"currency_pair": "BTC_USDT", "asks": [["50000", "invalid_size"]], "bids": [["49999", "0.1"]]}
            ]
            
            errors_handled = 0
            for test_data in test_cases:
                try:
                    await client._handle_orderbook(test_data)
                    # 如果没有抛出异常，说明错误被正确处理了
                    errors_handled += 1
                except Exception:
                    # 如果抛出异常，这不一定是坏事，但需要记录
                    pass
            
            if errors_handled < len(test_cases) // 2:
                return {"passed": False, "error": "Error handling not robust enough"}
            
            return {
                "passed": True,
                "details": f"Error handling robust: {errors_handled}/{len(test_cases)} cases handled gracefully"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Error handling test failed: {str(e)}"
            }

    async def test_parameter_validation(self):
        """测试参数验证"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            # 测试不同的初始化参数
            valid_clients = []
            
            # 有效的市场类型
            for market_type in ["spot", "futures"]:
                client = GateWebSocketClient(market_type)
                if client.market_type == market_type:
                    valid_clients.append(client)
            
            if len(valid_clients) != 2:
                return {"passed": False, "error": "Market type validation failed"}
            
            # 测试符号设置
            client = GateWebSocketClient("spot")
            
            # 测试有效符号
            valid_symbols = ["BTC_USDT", "ETH_USDT"]
            client.set_symbols(valid_symbols)
            
            if not client.symbols:
                return {"passed": False, "error": "Valid symbols not set correctly"}
            
            # 测试空符号列表
            client.set_symbols([])
            if not client.symbols:  # 应该有默认符号
                return {"passed": False, "error": "Empty symbols should have defaults"}
            
            return {
                "passed": True,
                "details": "Parameter validation working correctly"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Parameter validation test failed: {str(e)}"
            }

    async def test_system_integration(self):
        """系统集成测试"""
        print("\n🔄 === 系统集成测试 ===")
        
        tests = [
            self.test_multi_exchange_consistency,
            self.test_data_flow_integration,
            self.test_timestamp_synchronization
        ]
        
        for test in tests:
            try:
                result = await test()
                test_name = test.__name__
                self.test_results["system_integration_tests"][test_name] = result
                if result.get("passed", False):
                    self.total_passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    self.total_failed += 1
                    print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
            except Exception as e:
                self.total_failed += 1
                error_msg = f"Exception in {test.__name__}: {str(e)}"
                print(f"🚨 {error_msg}")
                self.test_results["critical_errors"].append(error_msg)

    async def test_multi_exchange_consistency(self):
        """测试多交易所一致性"""
        try:
            # 导入三个交易所的WebSocket客户端
            from websocket.gate_ws import GateWebSocketClient
            from websocket.bybit_ws import BybitWebSocketClient  
            from websocket.okx_ws import OKXWebSocketClient
            
            # 创建客户端实例
            gate_client = GateWebSocketClient("spot")
            bybit_client = BybitWebSocketClient("spot")
            okx_client = OKXWebSocketClient("spot")
            
            clients = {
                "gate": gate_client,
                "bybit": bybit_client,
                "okx": okx_client
            }
            
            # 检查统一接口
            for exchange, client in clients.items():
                # 检查必需的方法
                required_methods = ["set_symbols", "get_status", "send_heartbeat"]
                for method in required_methods:
                    if not hasattr(client, method) or not callable(getattr(client, method)):
                        return {"passed": False, "error": f"{exchange} missing method {method}"}
                
                # 检查统一属性
                required_attrs = ["market_type", "symbols"]
                for attr in required_attrs:
                    if not hasattr(client, attr):
                        return {"passed": False, "error": f"{exchange} missing attribute {attr}"}
            
            return {
                "passed": True,
                "details": "Multi-exchange consistency verified"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "critical": True,
                "error": f"Multi-exchange consistency test failed: {str(e)}"
            }

    async def test_data_flow_integration(self):
        """测试数据流集成"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            from websocket.unified_data_formatter import get_orderbook_formatter
            
            client = GateWebSocketClient("spot")
            formatter = get_orderbook_formatter()
            
            # 测试数据流是否能正确通过格式化器
            test_data = {
                "asks": [[50000.1, 0.1], [50000.2, 0.2]],
                "bids": [[49999.9, 0.1], [49999.8, 0.2]],
                "symbol": "BTC-USDT",
                "exchange": "gate",
                "market_type": "spot",
                "timestamp": int(time.time() * 1000)
            }
            
            formatted_data = formatter.format_orderbook_data(**test_data)
            
            if not formatted_data:
                return {"passed": False, "error": "Data formatter returned empty result"}
            
            # 验证格式化后的数据结构
            if "asks" not in formatted_data or "bids" not in formatted_data:
                return {"passed": False, "error": "Formatted data missing asks/bids"}
            
            return {
                "passed": True,
                "details": "Data flow integration working correctly"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Data flow integration test failed: {str(e)}"
            }

    async def test_timestamp_synchronization(self):
        """测试时间戳同步"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            
            # 测试时间戳处理器
            current_time = int(time.time() * 1000)
            test_data = {"timestamp": current_time}
            
            synced_timestamp = client.timestamp_processor.get_synced_timestamp(test_data)
            
            # 验证时间戳合理性（不能偏差太大）
            time_diff = abs(synced_timestamp - current_time)
            if time_diff > 60000:  # 1分钟
                return {"passed": False, "error": f"Timestamp difference too large: {time_diff}ms"}
            
            return {
                "passed": True,
                "details": f"Timestamp synchronization working, diff: {time_diff}ms"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Timestamp synchronization test failed: {str(e)}"
            }

    async def test_production_environment(self):
        """生产环境测试"""
        print("\n🚀 === 生产环境测试 ===")
        
        tests = [
            self.test_performance_benchmarks,
            self.test_concurrent_processing,
            self.test_memory_usage
        ]
        
        for test in tests:
            try:
                result = await test()
                test_name = test.__name__
                self.test_results["production_environment_tests"][test_name] = result
                if result.get("passed", False):
                    self.total_passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    self.total_failed += 1
                    print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
            except Exception as e:
                self.total_failed += 1
                error_msg = f"Exception in {test.__name__}: {str(e)}"
                print(f"🚨 {error_msg}")
                self.test_results["critical_errors"].append(error_msg)

    async def test_performance_benchmarks(self):
        """测试性能基准"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT"])
            
            # 性能测试数据
            test_data = {
                "currency_pair": "BTC_USDT",
                "asks": [["50000.1", "0.1"]] * 30,  # 30档深度
                "bids": [["49999.9", "0.1"]] * 30,
                "timestamp": int(time.time() * 1000)
            }
            
            # 测试处理时间
            iterations = 100
            total_time = 0
            
            for _ in range(iterations):
                start_time = time.time()
                await client._handle_orderbook(test_data)
                end_time = time.time()
                total_time += (end_time - start_time) * 1000  # 转换为毫秒
            
            avg_processing_time = total_time / iterations
            
            # 性能要求：每次处理不超过100毫秒
            if avg_processing_time > 100:
                return {
                    "passed": False,
                    "error": f"Performance too slow: {avg_processing_time:.2f}ms average"
                }
            
            return {
                "passed": True,
                "details": f"Performance benchmark passed: {avg_processing_time:.2f}ms average processing time"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Performance benchmark test failed: {str(e)}"
            }

    async def test_concurrent_processing(self):
        """测试并发处理"""
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT", "ETH_USDT"])
            
            # 创建并发处理任务
            async def process_orderbook(symbol, iteration):
                test_data = {
                    "currency_pair": symbol,
                    "asks": [["50000.1", "0.1"]],
                    "bids": [["49999.9", "0.1"]],
                    "timestamp": int(time.time() * 1000)
                }
                await client._handle_orderbook(test_data)
                return f"{symbol}_{iteration}"
            
            # 创建并发任务
            tasks = []
            for i in range(50):  # 50个并发任务
                symbol = "BTC_USDT" if i % 2 == 0 else "ETH_USDT"
                tasks.append(process_orderbook(symbol, i))
            
            # 执行并发测试
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 检查结果
            successful_tasks = sum(1 for r in results if not isinstance(r, Exception))
            failed_tasks = len(results) - successful_tasks
            
            if failed_tasks > len(tasks) * 0.1:  # 允许10%失败率
                return {
                    "passed": False,
                    "error": f"Too many concurrent failures: {failed_tasks}/{len(tasks)}"
                }
            
            total_time = (end_time - start_time) * 1000
            return {
                "passed": True,
                "details": f"Concurrent processing: {successful_tasks}/{len(tasks)} successful in {total_time:.2f}ms"
            }
            
        except Exception as e:
            return {
                "passed": False,
                "error": f"Concurrent processing test failed: {str(e)}"
            }

    async def test_memory_usage(self):
        """测试内存使用"""
        try:
            import psutil
            import gc
            
            # 记录初始内存
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            from websocket.gate_ws import GateWebSocketClient
            
            # 创建多个客户端进行内存测试
            clients = []
            for i in range(10):
                client = GateWebSocketClient("spot")
                client.set_symbols([f"BTC_USDT", f"ETH_USDT"])
                clients.append(client)
            
            # 模拟大量数据处理
            for client in clients:
                for _ in range(100):
                    test_data = {
                        "currency_pair": "BTC_USDT",
                        "asks": [["50000.1", "0.1"]] * 30,
                        "bids": [["49999.9", "0.1"]] * 30,
                        "timestamp": int(time.time() * 1000)
                    }
                    await client._handle_orderbook(test_data)
            
            # 清理客户端
            clients.clear()
            gc.collect()
            
            # 记录最终内存
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # 内存增长不应超过100MB
            if memory_increase > 100:
                return {
                    "passed": False,
                    "error": f"Memory usage too high: {memory_increase:.2f}MB increase"
                }
            
            return {
                "passed": True,
                "details": f"Memory usage acceptable: {memory_increase:.2f}MB increase"
            }
            
        except ImportError:
            return {
                "passed": True,
                "details": "psutil not available, skipping memory test"
            }
        except Exception as e:
            return {
                "passed": False,
                "error": f"Memory usage test failed: {str(e)}"
            }

    async def generate_test_report(self):
        """生成测试报告"""
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["total_tests"] = self.total_passed + self.total_failed
        self.test_results["success_rate"] = (self.total_passed / (self.total_passed + self.total_failed)) * 100 if (self.total_passed + self.total_failed) > 0 else 0
        
        # 保存报告
        report_file = f"websocket_fix_validation_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 === 测试报告 ===")
        print(f"总测试数: {self.test_results['total_tests']}")
        print(f"通过: {self.total_passed}")
        print(f"失败: {self.total_failed}")
        print(f"成功率: {self.test_results['success_rate']:.1f}%")
        
        if self.test_results["critical_errors"]:
            print(f"\n🚨 关键错误:")
            for error in self.test_results["critical_errors"]:
                print(f"  - {error}")
        
        print(f"\n📋 详细报告已保存到: {report_file}")
        
        # 验证修复质量
        if self.test_results["success_rate"] < 95:
            print(f"\n❌ 修复质量不达标! 成功率仅{self.test_results['success_rate']:.1f}%")
            return False
        elif self.test_results["critical_errors"]:
            print(f"\n⚠️ 存在{len(self.test_results['critical_errors'])}个关键错误，需要修复")
            return False
        else:
            print(f"\n✅ 修复质量达标! Gate.io数据阻塞问题已100%解决")
            return True

async def main():
    """主函数"""
    validator = WebSocketFixComprehensiveValidation()
    success = await validator.run_all_tests()
    
    if not success:
        sys.exit(1)  # 测试失败时退出
    
    print("\n🎉 所有测试通过，修复质量验证完成！")

if __name__ == "__main__":
    asyncio.run(main())