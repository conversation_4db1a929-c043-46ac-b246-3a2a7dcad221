#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 简单验证测试 - 验证统一修复效果
"""

import asyncio
import time
import logging
import sys
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_gate_connection():
    """测试Gate.io连接"""
    try:
        from websocket.gate_ws import GateWebSocketClient
        
        logger.info("🔧 测试Gate.io连接...")
        client = GateWebSocketClient("spot")
        client.set_symbols(["BTC-USDT"])
        
        data_count = 0
        
        async def on_data(data):
            nonlocal data_count
            data_count += 1
            if data_count <= 5:
                logger.info(f"📊 Gate.io数据 #{data_count}: 收到订单簿数据")
        
        client.register_callback("orderbook", on_data)
        
        # 启动连接
        start_time = time.time()
        task = asyncio.create_task(client.run())
        
        # 等待30秒
        await asyncio.sleep(30)
        
        # 检查结果
        elapsed = time.time() - start_time
        rate = data_count / elapsed if elapsed > 0 else 0
        
        logger.info(f"✅ Gate.io测试完成: {data_count}条数据, 频率={rate:.2f}/s")
        
        task.cancel()
        return data_count > 0, rate
        
    except Exception as e:
        logger.error(f"❌ Gate.io测试失败: {e}")
        return False, 0

async def test_okx_connection():
    """测试OKX连接"""
    try:
        from websocket.okx_ws import OKXWebSocketClient
        
        logger.info("🔧 测试OKX连接...")
        client = OKXWebSocketClient("spot")
        client.set_symbols(["BTC-USDT"])
        
        data_count = 0
        
        async def on_data(data):
            nonlocal data_count
            data_count += 1
            if data_count <= 5:
                logger.info(f"📊 OKX数据 #{data_count}: 收到订单簿数据")
        
        client.register_callback("orderbook", on_data)
        
        # 启动连接
        start_time = time.time()
        task = asyncio.create_task(client.run())
        
        # 等待30秒
        await asyncio.sleep(30)
        
        # 检查结果
        elapsed = time.time() - start_time
        rate = data_count / elapsed if elapsed > 0 else 0
        
        logger.info(f"✅ OKX测试完成: {data_count}条数据, 频率={rate:.2f}/s")
        
        task.cancel()
        return data_count > 0, rate
        
    except Exception as e:
        logger.error(f"❌ OKX测试失败: {e}")
        return False, 0

async def main():
    """主测试函数"""
    logger.info("🚀 开始简单验证测试")
    
    # 测试Gate.io
    logger.info("\n" + "="*50)
    logger.info("测试 Gate.io")
    logger.info("="*50)
    gate_success, gate_rate = await test_gate_connection()
    
    # 等待间隔
    await asyncio.sleep(5)
    
    # 测试OKX
    logger.info("\n" + "="*50)
    logger.info("测试 OKX")
    logger.info("="*50)
    okx_success, okx_rate = await test_okx_connection()
    
    # 生成报告
    logger.info("\n" + "="*60)
    logger.info("🔥 测试结果报告")
    logger.info("="*60)
    
    gate_status = "✅ 正常" if gate_success and gate_rate > 1 else "❌ 异常"
    okx_status = "✅ 正常" if okx_success and okx_rate > 1 else "❌ 异常"
    
    logger.info(f"Gate.io: {gate_status} (频率: {gate_rate:.2f}/s)")
    logger.info(f"OKX:     {okx_status} (频率: {okx_rate:.2f}/s)")
    
    if gate_success and okx_success and gate_rate > 1 and okx_rate > 1:
        logger.info("🎉 统一修复验证成功！数据阻塞问题已解决")
        return True
    else:
        logger.error("❌ 统一修复验证失败！仍存在数据阻塞问题")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        sys.exit(1)
