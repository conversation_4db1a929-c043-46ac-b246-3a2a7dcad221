#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gate.io数据阻塞专项修复验证
测试5649ms阻塞问题是否已完全解决
"""

import asyncio
import time
import sys
import os
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class GateDataBlockingFixValidation:
    """Gate.io数据阻塞修复专项验证"""

    def __init__(self):
        self.results = {
            "test_start": datetime.now().isoformat(),
            "gate_processing_times": [],
            "blocking_detected": False,
            "max_processing_time": 0,
            "avg_processing_time": 0,
            "tests_passed": 0,
            "tests_failed": 0
        }
        self.tests_passed = 0
        self.tests_failed = 0

    async def test_gate_data_blocking_fix(self):
        """测试Gate.io数据阻塞修复"""
        print("🔥 开始Gate.io数据阻塞专项修复验证...")
        
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            # 创建Gate.io客户端
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT", "ETH_USDT"])
            
            # 模拟实际的大订单簿数据
            large_orderbook_data = {
                "currency_pair": "BTC_USDT",
                "asks": [],
                "bids": [],
                "timestamp": int(time.time() * 1000)
            }
            
            # 生成50档深度数据（模拟真实场景）
            base_ask_price = 50000.0
            base_bid_price = 49999.0
            
            for i in range(50):
                ask_price = base_ask_price + i * 0.1
                bid_price = base_bid_price - i * 0.1
                large_orderbook_data["asks"].append([str(ask_price), "0.1"])
                large_orderbook_data["bids"].append([str(bid_price), "0.1"])
            
            # 测试100次处理，记录每次的处理时间
            processing_times = []
            
            print(f"📊 测试100次大订单簿数据处理...")
            
            for i in range(100):
                start_time = time.time()
                
                # 处理订单簿数据
                await client._handle_orderbook(large_orderbook_data)
                
                end_time = time.time()
                processing_time_ms = (end_time - start_time) * 1000
                processing_times.append(processing_time_ms)
                
                # 实时监控处理时间
                if processing_time_ms > 1000:  # 超过1秒算阻塞
                    print(f"🚨 检测到数据阻塞: 第{i+1}次处理耗时 {processing_time_ms:.2f}ms")
                    self.results["blocking_detected"] = True
                
                if i % 10 == 9:  # 每10次报告一次进度
                    avg_time = sum(processing_times[-10:]) / 10
                    print(f"📈 进度 {i+1}/100: 最近10次平均处理时间 {avg_time:.2f}ms")
            
            # 计算统计数据
            self.results["gate_processing_times"] = processing_times
            self.results["max_processing_time"] = max(processing_times)
            self.results["avg_processing_time"] = sum(processing_times) / len(processing_times)
            self.results["min_processing_time"] = min(processing_times)
            
            # 验证修复效果
            print(f"\n📊 Gate.io数据处理性能统计:")
            print(f"   最大处理时间: {self.results['max_processing_time']:.2f}ms")
            print(f"   平均处理时间: {self.results['avg_processing_time']:.2f}ms")
            print(f"   最小处理时间: {self.results['min_processing_time']:.2f}ms")
            
            # 判定修复是否成功
            if self.results["max_processing_time"] > 1000:
                print(f"❌ Gate.io数据阻塞问题未完全解决! 最大处理时间: {self.results['max_processing_time']:.2f}ms")
                self.results["fix_successful"] = False
                self.tests_failed += 1
            elif self.results["avg_processing_time"] > 100:
                print(f"⚠️ Gate.io平均处理时间偏高: {self.results['avg_processing_time']:.2f}ms")
                self.results["fix_successful"] = True
                self.results["performance_warning"] = True
                self.tests_passed += 1
            else:
                print(f"✅ Gate.io数据阻塞问题完全解决! 处理时间优秀: {self.results['avg_processing_time']:.2f}ms")
                self.results["fix_successful"] = True
                self.tests_passed += 1
            
            return self.results["fix_successful"]
            
        except Exception as e:
            print(f"🚨 Gate.io数据阻塞测试失败: {str(e)}")
            self.results["test_error"] = str(e)
            self.results["fix_successful"] = False
            self.tests_failed += 1
            return False

    async def test_memory_leak_prevention(self):
        """测试内存泄漏预防"""
        print(f"\n🧠 测试内存泄漏预防...")
        
        try:
            import psutil
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            from websocket.gate_ws import GateWebSocketClient
            
            # 创建并销毁多个客户端
            for i in range(20):
                client = GateWebSocketClient("spot")
                client.set_symbols(["BTC_USDT"])
                
                # 处理一些数据
                test_data = {
                    "currency_pair": "BTC_USDT",
                    "asks": [["50000.1", "0.1"]] * 30,
                    "bids": [["49999.9", "0.1"]] * 30,
                    "timestamp": int(time.time() * 1000)
                }
                
                for _ in range(10):
                    await client._handle_orderbook(test_data)
                
                # 显式删除客户端
                del client
            
            import gc
            gc.collect()
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"   内存使用: 初始 {initial_memory:.1f}MB -> 最终 {final_memory:.1f}MB")
            print(f"   内存增长: {memory_increase:.1f}MB")
            
            if memory_increase > 50:  # 50MB限制
                print(f"❌ 内存泄漏检测到: {memory_increase:.1f}MB")
                self.tests_failed += 1
                return False
            else:
                print(f"✅ 内存使用正常: {memory_increase:.1f}MB")
                self.tests_passed += 1
                return True
                
        except ImportError:
            print("ℹ️ psutil不可用，跳过内存测试")
            return True
        except Exception as e:
            print(f"🚨 内存测试失败: {str(e)}")
            self.tests_failed += 1
            return False

    async def test_concurrent_symbol_processing(self):
        """测试并发符号处理"""
        print(f"\n🔄 测试并发符号处理...")
        
        try:
            from websocket.gate_ws import GateWebSocketClient
            
            client = GateWebSocketClient("spot")
            client.set_symbols(["BTC_USDT", "ETH_USDT", "ADA_USDT"])
            
            # 并发处理不同符号的订单簿数据
            async def process_symbol_data(symbol, count):
                processing_times = []
                for i in range(count):
                    test_data = {
                        "currency_pair": symbol,
                        "asks": [["50000.1", "0.1"]] * 20,
                        "bids": [["49999.9", "0.1"]] * 20,
                        "timestamp": int(time.time() * 1000)
                    }
                    
                    start = time.time()
                    await client._handle_orderbook(test_data)
                    end = time.time()
                    
                    processing_times.append((end - start) * 1000)
                
                return {
                    "symbol": symbol,
                    "avg_time": sum(processing_times) / len(processing_times),
                    "max_time": max(processing_times),
                    "count": count
                }
            
            # 创建并发任务
            tasks = [
                process_symbol_data("BTC_USDT", 30),
                process_symbol_data("ETH_USDT", 30),
                process_symbol_data("ADA_USDT", 30)
            ]
            
            # 执行并发测试
            start_time = time.time()
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = (end_time - start_time) * 1000
            
            print(f"   并发测试结果:")
            blocking_detected = False
            for result in results:
                print(f"     {result['symbol']}: 平均 {result['avg_time']:.2f}ms, 最大 {result['max_time']:.2f}ms")
                if result['max_time'] > 1000:
                    blocking_detected = True
            
            print(f"   总耗时: {total_time:.2f}ms")
            
            if blocking_detected:
                print(f"❌ 并发处理中检测到阻塞")
                self.tests_failed += 1
                return False
            else:
                print(f"✅ 并发处理正常，无阻塞现象")
                self.tests_passed += 1
                return True
                
        except Exception as e:
            print(f"🚨 并发测试失败: {str(e)}")
            self.tests_failed += 1
            return False

    async def generate_final_report(self):
        """生成最终报告"""
        self.results["test_end"] = datetime.now().isoformat()
        self.results["total_tests"] = self.tests_passed + self.tests_failed
        
        if self.tests_failed == 0:
            success_rate = 100.0
            overall_status = "✅ PASSED"
        else:
            success_rate = (self.tests_passed / (self.tests_passed + self.tests_failed)) * 100
            overall_status = "❌ FAILED"
        
        self.results["success_rate"] = success_rate
        self.results["overall_status"] = overall_status
        
        # 保存详细报告
        report_file = f"gate_blocking_fix_validation_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n" + "="*60)
        print(f"🎯 Gate.io数据阻塞修复验证报告")
        print(f"="*60)
        
        if self.results.get("fix_successful", False):
            print(f"✅ Gate.io数据阻塞问题已100%解决")
            print(f"   - 最大处理时间: {self.results.get('max_processing_time', 0):.2f}ms")
            print(f"   - 平均处理时间: {self.results.get('avg_processing_time', 0):.2f}ms")
            print(f"   - 从5649ms降低到{self.results.get('avg_processing_time', 0):.2f}ms")
        else:
            print(f"❌ Gate.io数据阻塞问题未完全解决")
        
        print(f"\n📊 测试统计:")
        print(f"   总测试: {self.results['total_tests']}")
        print(f"   通过: {self.tests_passed}")
        print(f"   失败: {self.tests_failed}")
        print(f"   成功率: {success_rate:.1f}%")
        
        print(f"\n📋 详细报告: {report_file}")
        print(f"="*60)
        
        return self.results.get("fix_successful", False)

async def main():
    """主验证函数"""
    validator = GateDataBlockingFixValidation()
    
    # 执行所有测试
    test1 = await validator.test_gate_data_blocking_fix()
    test2 = await validator.test_memory_leak_prevention()
    test3 = await validator.test_concurrent_symbol_processing()
    
    # 生成最终报告
    success = await validator.generate_final_report()
    
    if not success:
        print(f"\n🚨 Gate.io数据阻塞修复验证失败!")
        sys.exit(1)
    else:
        print(f"\n🎉 Gate.io数据阻塞修复验证100%通过!")

if __name__ == "__main__":
    asyncio.run(main())