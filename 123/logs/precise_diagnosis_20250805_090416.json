{"diagnosis_timestamp": "2025-08-05T09:04:16.891653", "duration_seconds": 0.0, "configuration_issues": [{"config_file": "settings.py", "parameter": "ws_connect_timeout", "value": 1000, "unit": "毫秒", "expected_value": 10, "expected_unit": "秒", "severity": "CRITICAL", "description": "settings.py中ws_connect_timeout使用毫秒单位(1000ms)，与其他配置文件不一致"}, {"config_file": "network_config.py", "parameter": "ws_connect_timeout", "value": 10, "unit": "秒", "expected_value": 10, "expected_unit": "秒", "severity": "MEDIUM", "description": "network_config.py中ws_connect_timeout使用秒单位，与settings.py不一致"}, {"config_file": ".env.sample", "parameter": "WS_CONNECTION_TIMEOUT", "value": 10, "unit": "秒", "expected_value": 10, "expected_unit": "秒", "severity": "MEDIUM", "description": ".env.sample中WS_CONNECTION_TIMEOUT使用秒单位，参数名与其他文件不一致"}, {"config_file": "ws_client.py", "parameter": "connect_warning_threshold", "value": "ws_connect_timeout / 1000.0", "unit": "错误转换", "expected_value": "正确的单位转换", "expected_unit": "秒", "severity": "CRITICAL", "description": "ws_client.py中单位转换错误，如果ws_connect_timeout是秒，除以1000会得到毫秒级阈值"}], "architecture_issues": [{"component": "WebSocket数据处理链路", "issue_type": "complexity", "description": "数据处理链路过长，包含6个处理层级", "impact": "数据在处理过程中老化，导致30-60秒延迟", "processing_layers": 6, "severity": "CRITICAL"}, {"component": "消息队列", "issue_type": "bottleneck", "description": "消息队列成为处理瓶颈，数据排队等待处理", "impact": "高频数据在队列中积压，导致处理延迟", "processing_layers": 1, "severity": "HIGH"}, {"component": "连接管理", "issue_type": "redundant", "description": "存在重复的连接管理组件: 统一连接池管理器, Gate专用WebSocket客户端, OKX专用WebSocket客户端", "impact": "功能重复导致维护困难和潜在冲突", "processing_layers": 3, "severity": "MEDIUM"}, {"component": "连接重连机制", "issue_type": "bottleneck", "description": "配置错误导致连接超时阈值过小，引发频繁重连", "impact": "连接风暴导致数据流中断和阻塞", "processing_layers": 1, "severity": "HIGH"}, {"component": "时间戳处理", "issue_type": "bottleneck", "description": "数据在消息队列中等待处理时，时间戳逐渐老化", "impact": "老化的时间戳被误判为过期数据，导致数据丢弃", "processing_layers": 1, "severity": "HIGH"}], "total_issues": 9, "critical_issues": 3, "recommendations": ["🔥 CRITICAL: 立即统一WebSocket连接超时配置，所有配置文件使用相同单位(秒)和数值", "🔥 CRITICAL: 修复ws_client.py中的单位转换错误，移除错误的/1000.0计算", "🔥 HIGH: 简化WebSocket数据处理架构，从6层减少到2-3层", "🔥 HIGH: 移除消息队列瓶颈，实现WebSocket数据直接传递给差价计算器", "🔥 MEDIUM: 整合重复的连接管理组件，使用统一的连接池管理器"], "summary": {"total_configuration_issues": 4, "total_architecture_issues": 5, "critical_issues": 3, "high_issues": 3, "medium_issues": 3, "primary_root_causes": ["配置系统混乱 - 多配置源不一致", "架构过度复杂 - 6层处理导致延迟", "连接管理问题 - 频繁重连风暴", "时间戳老化 - 数据在队列中老化"]}}