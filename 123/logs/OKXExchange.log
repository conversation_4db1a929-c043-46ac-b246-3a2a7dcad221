2025-08-05 08:50:51.453 [INFO] [exchanges.okx_exchange] ✅ OKX API限速使用统一配置: 1次/秒
2025-08-05 08:50:51 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-05 08:50:51 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-05 08:50:51 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-05 08:50:51 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-05 08:50:51.453 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=1次/秒
2025-08-05 08:50:51 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-05 08:50:51 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-05 08:50:51 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-05 08:50:52 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-05 08:50:52.087 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:50:52.612 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:50:52.612 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:50:53.089 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:50:53.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:50:53 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ADA-USDT
2025-08-05 08:50:53.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:50:53.584 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:50:53.584 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:50:54.083 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:50:54.083 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:50:54 [DEBUG] [OKXExchange] OKX预设置杠杆成功: DOGE-USDT
2025-08-05 08:50:54.083 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:50:54.582 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:50:54.582 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:50:55.080 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:50:55.080 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:50:55 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SOL-USDT
2025-08-05 08:50:55.080 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:50:55.588 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:50:55.588 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:50:56.079 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:50:56.080 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:50:56 [DEBUG] [OKXExchange] OKX预设置杠杆成功: AVAX-USDT
2025-08-05 08:50:56 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-05 08:50:56.580 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.588 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.597 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.598 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.608 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.612 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 08:50:56.625 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 08:51:21.834 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:21.834 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:51:22.840 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:22.840 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:51:23.832 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:23.832 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:51:24.890 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:24.891 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:51:25.830 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:25.831 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:51:26.839 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:51:26.839 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:11.349 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 08:52:14.934 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 08:52:16.419 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 08:52:16.922 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 08:52:16.932 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 08:52:16.942 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 08:52:19.020 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 08:52:20.443 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ADA-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.444 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOGE-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.444 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.444 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AVAX-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.444 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.445 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SHIB-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.445 [INFO] [exchanges.okx_exchange] OKX设置杠杆: BNB-USDT-SWAP 3倍，保证金模式: cross
2025-08-05 08:52:20.539 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:20.540 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:21.023 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:21.023 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:21.024 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:21.024 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:21.026 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:21.026 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:21.026 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:21.026 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:21.038 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:21.038 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:21.041 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-05 08:52:21.042 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-05 08:52:21.042 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-05 08:52:21.042 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-05 08:52:21.042 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-05 08:52:21.042 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-05 08:52:21.043 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-05 08:52:21.043 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-05 08:52:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-05 08:52:21.053 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-05 08:52:21.053 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-05 08:52:21.053 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-05 08:52:21.054 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-05 08:52:21.054 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-05 08:52:21.054 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-05 08:52:21.054 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-05 08:52:21.054 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-05 08:52:21 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-05 08:52:21.528 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:21.528 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:21.530 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:21.530 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:21.530 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:21.531 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:21.531 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:21.531 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:23.131 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:23.131 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:23.621 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:23.621 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:23.654 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-08-05 08:52:23.654 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-05 08:52:24.125 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-05 08:52:24.125 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-05 08:52:42.372 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.856 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.858 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.859 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.859 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.861 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 08:52:42.862 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.141 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: ADA-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.656 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SHIB-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.656 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.658 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.663 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOGE-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.669 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AVAX-USDT -> 最大杠杆=50x
2025-08-05 08:52:50.684 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: BNB-USDT -> 最大杠杆=50x
