#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 统一修复验证测试
验证Gate.io和OKX数据阻塞问题是否真正解决
"""

import asyncio
import time
import logging
import sys
import os
from datetime import datetime
from typing import Dict, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from websocket.gate_ws import GateWebSocketClient
from websocket.okx_ws import OKXWebSocketClient
from websocket.bybit_ws import BybitWebSocketClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedFixValidator:
    """统一修复验证器"""
    
    def __init__(self):
        self.test_symbols = ["BTC-USDT", "ETH-USDT"]  # Gate格式
        self.okx_symbols = ["BTC-USDT", "ETH-USDT"]   # OKX格式
        self.bybit_symbols = ["BTCUSDT", "ETHUSDT"]   # Bybit格式
        
        self.clients = {}
        self.data_counts = {
            "gate_spot": 0,
            "okx_spot": 0, 
            "bybit_spot": 0
        }
        self.start_time = time.time()
        self.test_duration = 300  # 5分钟测试
        
    async def setup_clients(self):
        """设置WebSocket客户端"""
        logger.info("🔧 设置WebSocket客户端...")
        
        try:
            # Gate.io现货
            self.clients["gate_spot"] = GateWebSocketClient("spot")
            self.clients["gate_spot"].set_symbols(self.test_symbols)
            self.clients["gate_spot"].register_callback("orderbook", self.on_gate_data)
            
            # OKX现货
            self.clients["okx_spot"] = OKXWebSocketClient("spot")
            self.clients["okx_spot"].set_symbols(self.okx_symbols)
            self.clients["okx_spot"].register_callback("orderbook", self.on_okx_data)
            
            # Bybit现货
            self.clients["bybit_spot"] = BybitWebSocketClient("spot")
            self.clients["bybit_spot"].set_symbols(self.bybit_symbols)
            self.clients["bybit_spot"].register_callback("orderbook", self.on_bybit_data)
            
            logger.info("✅ 客户端设置完成")
            
        except Exception as e:
            logger.error(f"❌ 客户端设置失败: {e}")
            raise
    
    async def on_gate_data(self, data):
        """Gate.io数据回调"""
        self.data_counts["gate_spot"] += 1
        if self.data_counts["gate_spot"] % 100 == 0:
            elapsed = time.time() - self.start_time
            rate = self.data_counts["gate_spot"] / elapsed
            logger.info(f"📊 Gate.io: {self.data_counts['gate_spot']} 条数据, 频率={rate:.2f}/s")
    
    async def on_okx_data(self, data):
        """OKX数据回调"""
        self.data_counts["okx_spot"] += 1
        if self.data_counts["okx_spot"] % 100 == 0:
            elapsed = time.time() - self.start_time
            rate = self.data_counts["okx_spot"] / elapsed
            logger.info(f"📊 OKX: {self.data_counts['okx_spot']} 条数据, 频率={rate:.2f}/s")
    
    async def on_bybit_data(self, data):
        """Bybit数据回调"""
        self.data_counts["bybit_spot"] += 1
        if self.data_counts["bybit_spot"] % 100 == 0:
            elapsed = time.time() - self.start_time
            rate = self.data_counts["bybit_spot"] / elapsed
            logger.info(f"📊 Bybit: {self.data_counts['bybit_spot']} 条数据, 频率={rate:.2f}/s")
    
    async def start_connections(self):
        """启动所有连接"""
        logger.info("🚀 启动WebSocket连接...")
        
        tasks = []
        for name, client in self.clients.items():
            logger.info(f"启动 {name}...")
            task = asyncio.create_task(client.run())
            tasks.append(task)
            
            # 🔥 关键：使用统一间隔避免同时连接
            await asyncio.sleep(0.5)
        
        logger.info("✅ 所有连接已启动")
        return tasks
    
    async def monitor_data_flow(self):
        """监控数据流"""
        logger.info("📊 开始监控数据流...")
        
        last_counts = self.data_counts.copy()
        monitor_interval = 30  # 30秒监控间隔
        
        while time.time() - self.start_time < self.test_duration:
            await asyncio.sleep(monitor_interval)
            
            elapsed = time.time() - self.start_time
            logger.info(f"\n🕐 测试进行中... 已运行 {elapsed:.0f}秒")
            
            # 检查数据流
            for exchange in ["gate_spot", "okx_spot", "bybit_spot"]:
                current_count = self.data_counts[exchange]
                last_count = last_counts[exchange]
                
                if current_count > last_count:
                    rate = (current_count - last_count) / monitor_interval
                    total_rate = current_count / elapsed
                    logger.info(f"✅ {exchange}: +{current_count - last_count} 条数据, "
                              f"当前频率={rate:.2f}/s, 平均频率={total_rate:.2f}/s")
                else:
                    logger.warning(f"⚠️ {exchange}: 无新数据！可能存在阻塞问题")
            
            last_counts = self.data_counts.copy()
    
    async def generate_report(self):
        """生成测试报告"""
        elapsed = time.time() - self.start_time
        
        logger.info("\n" + "="*60)
        logger.info("🔥 统一修复验证测试报告")
        logger.info("="*60)
        logger.info(f"测试时长: {elapsed:.0f}秒")
        logger.info(f"测试交易对: {len(self.test_symbols)}个")
        
        total_data = 0
        for exchange, count in self.data_counts.items():
            rate = count / elapsed if elapsed > 0 else 0
            total_data += count
            
            status = "✅ 正常" if rate > 1.0 else "⚠️ 可能有问题" if rate > 0.1 else "❌ 严重问题"
            logger.info(f"{exchange}: {count} 条数据, 平均频率={rate:.2f}/s {status}")
        
        logger.info(f"总数据量: {total_data} 条")
        logger.info(f"系统总频率: {total_data/elapsed:.2f}/s")
        
        # 判断修复效果
        gate_rate = self.data_counts["gate_spot"] / elapsed
        okx_rate = self.data_counts["okx_spot"] / elapsed
        bybit_rate = self.data_counts["bybit_spot"] / elapsed
        
        if gate_rate > 10 and okx_rate > 10 and bybit_rate > 10:
            logger.info("🎉 修复验证成功！所有交易所数据流正常")
            return True
        elif gate_rate < 1 or okx_rate < 1:
            logger.error("❌ 修复验证失败！Gate.io或OKX仍存在阻塞问题")
            return False
        else:
            logger.warning("⚠️ 修复效果一般，需要进一步优化")
            return False
    
    async def run_test(self):
        """运行完整测试"""
        logger.info("🔥 开始统一修复验证测试")
        
        try:
            # 1. 设置客户端
            await self.setup_clients()
            
            # 2. 启动连接
            connection_tasks = await self.start_connections()
            
            # 3. 等待连接稳定
            logger.info("⏳ 等待连接稳定...")
            await asyncio.sleep(10)
            
            # 4. 开始监控
            self.start_time = time.time()  # 重置开始时间
            monitor_task = asyncio.create_task(self.monitor_data_flow())
            
            # 5. 等待测试完成
            await monitor_task
            
            # 6. 生成报告
            success = await self.generate_report()
            
            # 7. 清理
            for task in connection_tasks:
                task.cancel()
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}")
            return False

async def main():
    """主函数"""
    logger.info("🚀 启动统一修复验证测试")
    
    validator = UnifiedFixValidator()
    success = await validator.run_test()
    
    if success:
        logger.info("🎉 测试通过！修复生效")
        sys.exit(0)
    else:
        logger.error("❌ 测试失败！需要进一步修复")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
