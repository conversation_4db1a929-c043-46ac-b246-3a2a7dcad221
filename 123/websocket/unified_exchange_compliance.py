#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 统一交易所合规配置管理器
确保三交易所在遵循各自官方规范的前提下实现最大程度的一致性
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ExchangeType(Enum):
    """交易所类型枚举"""
    GATE = "gate"
    OKX = "okx" 
    BYBIT = "bybit"

@dataclass
class ExchangeCompliance:
    """交易所合规配置"""
    name: str
    subscription_interval: float  # 订阅间隔（秒）
    max_requests_per_second: float  # 最大请求频率
    batch_size: int  # 批次大小
    heartbeat_interval: int  # 心跳间隔（秒）
    data_update_frequency: str  # 数据更新频率
    official_limit_description: str  # 官方限制描述

class UnifiedExchangeComplianceManager:
    """统一交易所合规管理器"""
    
    # 🔥 基于官方文档的严格合规配置
    EXCHANGE_CONFIGS = {
        ExchangeType.GATE: ExchangeCompliance(
            name="Gate.io",
            subscription_interval=0.05,  # 50ms间隔，符合灵活要求
            max_requests_per_second=20.0,  # 灵活限制，保守估计
            batch_size=1,  # 必须逐个订阅
            heartbeat_interval=20,  # 统一20秒
            data_update_frequency="1000ms",  # 从100ms改为1000ms避免超高频
            official_limit_description="灵活限制，支持100ms/1000ms数据频率"
        ),
        
        ExchangeType.OKX: ExchangeCompliance(
            name="OKX",
            subscription_interval=0.34,  # 严格遵循3 req/s = 0.33秒/请求
            max_requests_per_second=3.0,  # 官方明确限制
            batch_size=8,  # 批量订阅优化
            heartbeat_interval=20,  # 统一20秒
            data_update_frequency="100ms",  # OKX默认频率
            official_limit_description="3 requests/second (官方明确限制)"
        ),
        
        ExchangeType.BYBIT: ExchangeCompliance(
            name="Bybit",
            subscription_interval=0.5,  # 严格遵循600 req/5min = 2 req/s
            max_requests_per_second=2.0,  # 600 requests/5分钟 = 2 req/s
            batch_size=10,  # 批量订阅优化
            heartbeat_interval=20,  # 官方推荐20秒
            data_update_frequency="100ms",  # Bybit默认频率
            official_limit_description="600 requests/5分钟 = 2 requests/second"
        )
    }
    
    @classmethod
    def get_compliance_config(cls, exchange: ExchangeType) -> ExchangeCompliance:
        """获取交易所合规配置"""
        return cls.EXCHANGE_CONFIGS[exchange]
    
    @classmethod
    def get_unified_config(cls) -> Dict[str, Any]:
        """获取统一配置（在合规前提下的一致性配置）"""
        return {
            # 🔥 统一的通用配置
            "heartbeat_interval": 20,  # 所有交易所统一20秒心跳
            "data_timeout": 30,  # 统一30秒数据超时
            "reconnect_delay": 2.0,  # 统一2秒重连延迟
            "max_reconnect_attempts": 5,  # 统一最大重连次数
            
            # 🔥 交易所特定的合规配置
            "exchange_specific": {
                "gate": {
                    "subscription_interval": cls.EXCHANGE_CONFIGS[ExchangeType.GATE].subscription_interval,
                    "batch_size": cls.EXCHANGE_CONFIGS[ExchangeType.GATE].batch_size,
                    "data_frequency": cls.EXCHANGE_CONFIGS[ExchangeType.GATE].data_update_frequency
                },
                "okx": {
                    "subscription_interval": cls.EXCHANGE_CONFIGS[ExchangeType.OKX].subscription_interval,
                    "batch_size": cls.EXCHANGE_CONFIGS[ExchangeType.OKX].batch_size,
                    "data_frequency": cls.EXCHANGE_CONFIGS[ExchangeType.OKX].data_update_frequency
                },
                "bybit": {
                    "subscription_interval": cls.EXCHANGE_CONFIGS[ExchangeType.BYBIT].subscription_interval,
                    "batch_size": cls.EXCHANGE_CONFIGS[ExchangeType.BYBIT].batch_size,
                    "data_frequency": cls.EXCHANGE_CONFIGS[ExchangeType.BYBIT].data_update_frequency
                }
            }
        }
    
    @classmethod
    def validate_compliance(cls, exchange: ExchangeType, 
                          subscription_interval: float,
                          requests_per_second: float) -> tuple[bool, str]:
        """验证配置是否符合交易所官方规范"""
        config = cls.get_compliance_config(exchange)
        
        # 检查订阅间隔
        if subscription_interval < (1.0 / config.max_requests_per_second):
            return False, f"{config.name}订阅间隔{subscription_interval}s违反官方{config.max_requests_per_second} req/s限制"
        
        # 检查请求频率
        if requests_per_second > config.max_requests_per_second:
            return False, f"{config.name}请求频率{requests_per_second} req/s超过官方{config.max_requests_per_second} req/s限制"
        
        return True, f"{config.name}配置符合官方规范"
    
    @classmethod
    def get_compliance_report(cls) -> str:
        """生成合规报告"""
        report = "🔥 三交易所合规配置报告\n"
        report += "=" * 50 + "\n\n"
        
        for exchange_type, config in cls.EXCHANGE_CONFIGS.items():
            report += f"📊 {config.name}\n"
            report += f"   订阅间隔: {config.subscription_interval}s\n"
            report += f"   最大频率: {config.max_requests_per_second} req/s\n"
            report += f"   批次大小: {config.batch_size}\n"
            report += f"   数据频率: {config.data_update_frequency}\n"
            report += f"   官方限制: {config.official_limit_description}\n"
            
            # 验证合规性
            is_compliant, message = cls.validate_compliance(
                exchange_type, 
                config.subscription_interval,
                config.max_requests_per_second
            )
            report += f"   合规状态: {'✅' if is_compliant else '❌'} {message}\n\n"
        
        # 统一性分析
        report += "🎯 统一性分析\n"
        report += "-" * 30 + "\n"
        report += f"心跳间隔: 统一20秒 ✅\n"
        report += f"数据超时: 统一30秒 ✅\n"
        report += f"重连延迟: 统一2秒 ✅\n"
        report += f"订阅间隔: 因官方规范不同而不同 ⚠️\n"
        report += f"批次大小: 因API特性不同而不同 ⚠️\n"
        
        return report

# 🔥 全局合规配置实例
UNIFIED_COMPLIANCE = UnifiedExchangeComplianceManager.get_unified_config()

def get_exchange_config(exchange_name: str) -> Dict[str, Any]:
    """获取指定交易所的合规配置"""
    exchange_map = {
        "gate": ExchangeType.GATE,
        "okx": ExchangeType.OKX,
        "bybit": ExchangeType.BYBIT
    }
    
    if exchange_name.lower() not in exchange_map:
        raise ValueError(f"不支持的交易所: {exchange_name}")
    
    exchange_type = exchange_map[exchange_name.lower()]
    compliance_config = UnifiedExchangeComplianceManager.get_compliance_config(exchange_type)
    
    return {
        "subscription_interval": compliance_config.subscription_interval,
        "max_requests_per_second": compliance_config.max_requests_per_second,
        "batch_size": compliance_config.batch_size,
        "heartbeat_interval": compliance_config.heartbeat_interval,
        "data_update_frequency": compliance_config.data_update_frequency,
        "official_limit": compliance_config.official_limit_description
    }

if __name__ == "__main__":
    # 生成并打印合规报告
    print(UnifiedExchangeComplianceManager.get_compliance_report())
